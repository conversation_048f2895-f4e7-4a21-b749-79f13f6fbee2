"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }var w=(t=>(t[t.loadFile=0]="loadFile",t[t.saveFile=1]="saveFile",t[t.loadJson=2]="loadJson",t[t.loadJsonComments=3]="loadJsonComments",t[t.saveJson=4]="saveJson",t[t.loadBuffer=5]="loadBuffer",t[t.saveBuffer=6]="saveBuffer",t))(w||{});var _fs = require('fs'); var _fs2 = _interopRequireDefault(_fs);var _path = require('path');var v=e=>!!T(e),C= exports.c =async e=>!!T(e),T=e=>{let r=_path.parse.call(void 0, e).dir;return r==""||_fs.existsSync.call(void 0, r)||_fs.mkdirSync.call(void 0, r,{recursive:!0}),_fs.existsSync.call(void 0, r)};var _worker_threads = require('worker_threads');var S=({callback:e,...r})=>new Promise((o,s)=>{let i;new (0, _worker_threads.Worker)(_path.resolve.call(void 0, __dirname,"./worker.js")).once("message",n=>i=n).once("exit",n=>n==0?o(i):s(n)).once("error",n=>s(n)).once("messageerror",n=>s(n)).postMessage(r)}).then(o=>(e==null||e(o),o),o=>{throw new Error(o)}),m= exports.d =S;var h={loadS:e=>{if(v(e))return _fs2.default.readFileSync(e,"utf8");throw new Error(`Can't read from ${e}`)},saveS:(e,r)=>{if(v(e))_fs2.default.writeFileSync(e,r,"utf8");else throw new Error(`Can't write to ${e}`)},load:async(e,r)=>{if(await C(e)){let o=await _fs2.default.promises.readFile(e,"utf8");return r==null||r(o),o}else throw new Error(`Can't read from ${e}`)},save:async(e,r,o)=>{if(await C(e))await _fs2.default.promises.writeFile(e,r,"utf8"),o==null||o();else throw new Error(`Can't write to ${e}`)},loadW:(e,r)=>m({method:0,path:e,callback:r}),saveW:(e,r,o)=>m({method:1,path:e,data:r,callback:o})},a= exports.e =h;var J={loadS:(e,r=!1)=>JSON.parse(r?k(a.loadS(e)):a.loadS(e)),saveS:(e,r)=>a.saveS(e,JSON.stringify(r)),load:async(e,r,o=!1)=>{let s=JSON.parse(o?k(await a.load(e)):await a.load(e));return r==null||r(s),s},save:async(e,r,o)=>{await a.save(e,JSON.stringify(r)),o==null||o()},loadW:(e,r,o=!1)=>m({method:o?3:2,path:e,callback:r}),saveW:(e,r,o)=>m({method:4,path:e,data:r,callback:o})},U= exports.f =J;function k(e){let r=(t,l)=>{let f=0;for(;t[l]==="\\";)l--,f++;return!(f%2)},o=!1,s=!1,i=!1,n=0,u="";for(let t=0;t<e.length;t++){let l=e[t]||"",f=e[t+1]||"",g=!(i||s);if(g&&l==='"'&&r(e,t-1)&&(o=!o),o)continue;let c=l+f;if(g&&c==="//")u+=e.slice(n,t),n=t,s=!0,t++;else if(s&&c===`\r
`){t++,s=!1,n=t;continue}else if(s&&l===`
`)s=!1,n=t;else if(g&&c==="/*"){u+=e.slice(n,t),n=t,i=!0,t++;continue}else if(i&&c==="*/"){t++,i=!1,n=t+1;continue}}return(u+(i||s?"":e.slice(n))).replace(/,(?!\s*?[{["'\w])/g,"")}exports.a = w; exports.b = v; exports.c = C; exports.d = m; exports.e = a; exports.f = U;
