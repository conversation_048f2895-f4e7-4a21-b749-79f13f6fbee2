/**
 * Simplified TypeScript type definitions for interactive-agent
 * Essential types for request-user-input tool only
 */
// Performance-optimized constants
export const TOOL_CATEGORIES = {
    INPUT: 'input',
    UTILITY: 'utility'
};
// Essential utility functions for creating tool results
export const ToolResultUtils = {
    /**
     * Create a successful text-only tool result
     */
    success(text, meta) {
        return {
            content: [{ type: 'text', text }],
            isError: false,
            _meta: meta
        };
    },
    /**
     * Create an error tool result
     */
    error(errorMessage, meta) {
        return {
            content: [{ type: 'text', text: errorMessage }],
            isError: true,
            _meta: meta
        };
    }
};
//# sourceMappingURL=types.js.map