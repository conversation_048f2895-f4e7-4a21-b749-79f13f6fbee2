branches:
  - main
  - name: next
    prerelease: true
plugins:
  - '@semantic-release/commit-analyzer':
      preset: conventionalcommits
  - '@semantic-release/release-notes-generator':
      preset: conventionalcommits
  - '@semantic-release/npm'
  - '@semantic-release/git':
      assets:
        - package.json
        - pnpm-lock.yaml
      message: "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"
  - '@semantic-release/github' # Handles creating GitHub Releases
