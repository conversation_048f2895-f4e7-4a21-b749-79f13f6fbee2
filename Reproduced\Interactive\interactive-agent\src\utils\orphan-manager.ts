import { ChildProcess } from 'child_process';

/**
 * Information about a tracked process
 */
export interface ProcessInfo {
  pid: number;
  process: ChildProcess;
  spawnTime: Date;
  lastHeartbeat: Date;
  sessionId?: string;
}

/**
 * Robust orphan process tracking and cleanup system
 */
export class OrphanManager {
  private static instance: OrphanManager | null = null;
  private readonly processes = new Map<number, ProcessInfo>();
  private readonly maxTrackedProcesses = 100;
  private staleCheckInterval: NodeJS.Timeout | null = null;
  private signalHandlersRegistered = false;

  private constructor() {
    this.registerSignalHandlers();
    this.startStaleProcessMonitoring();
  }

  /**
   * Get singleton instance of OrphanManager
   */
  public static getInstance(): OrphanManager {
    if (!OrphanManager.instance) {
      OrphanManager.instance = new OrphanManager();
    }
    return OrphanManager.instance;
  }

  /**
   * Register a process for tracking
   */
  public registerProcess(process: ChildProcess, sessionId?: string): void {
    if (!process.pid) {
      console.warn('Cannot register process without PID');
      return;
    }

    // Enforce process limit
    if (this.processes.size >= this.maxTrackedProcesses) {
      console.warn(`Process registry full (${this.maxTrackedProcesses}), cleaning stale processes`);
      this.cleanupStaleProcesses(30000); // 30 seconds
    }

    const processInfo: ProcessInfo = {
      pid: process.pid,
      process,
      spawnTime: new Date(),
      lastHeartbeat: new Date(),
      sessionId
    };

    this.processes.set(process.pid, processInfo);

    // Set up automatic cleanup when process exits normally
    process.on('exit', () => {
      this.unregisterProcess(process.pid!);
    });

    console.debug(`Registered process ${process.pid} for tracking`, { sessionId });
  }

  /**
   * Remove a process from tracking
   */
  public unregisterProcess(pid: number): void {
    if (this.processes.delete(pid)) {
      console.debug(`Unregistered process ${pid} from tracking`);
    }
  }

  /**
   * Update heartbeat timestamp for a process
   */
  public updateHeartbeat(pid: number): void {
    const processInfo = this.processes.get(pid);
    if (processInfo) {
      processInfo.lastHeartbeat = new Date();
    }
  }

  /**
   * Check if a process is still alive
   */
  public checkProcessHealth(pid: number): boolean {
    try {
      // Sending signal 0 checks if process exists without actually sending a signal
      process.kill(pid, 0);
      return true;
    } catch (error) {
      // Process doesn't exist or we don't have permission
      return false;
    }
  }

  /**
   * Find processes that haven't had activity within the timeout
   */
  public findStaleProcesses(timeoutMs: number): ProcessInfo[] {
    const now = new Date();
    const staleProcesses: ProcessInfo[] = [];

    for (const processInfo of this.processes.values()) {
      const timeSinceHeartbeat = now.getTime() - processInfo.lastHeartbeat.getTime();
      
      if (timeSinceHeartbeat > timeoutMs) {
        // Double-check if process is actually dead
        if (!this.checkProcessHealth(processInfo.pid)) {
          staleProcesses.push(processInfo);
        } else {
          // Process is alive, update heartbeat
          this.updateHeartbeat(processInfo.pid);
        }
      }
    }

    return staleProcesses;
  }

  /**
   * Kill a specific process gracefully then forcefully
   */
  public async killProcess(pid: number, signal: NodeJS.Signals = 'SIGTERM'): Promise<boolean> {
    const processInfo = this.processes.get(pid);
    if (!processInfo) {
      console.warn(`Process ${pid} not found in registry`);
      return false;
    }

    try {
      console.debug(`Attempting to kill process ${pid} with ${signal}`);
      
      // Try graceful termination first
      if (this.isWindows()) {
        // Windows doesn't support SIGTERM, use taskkill
        await this.killWindowsProcess(pid);
      } else {
        // POSIX systems
        process.kill(pid, signal);
        
        // Wait a bit for graceful shutdown
        await this.delay(3000);
        
        // If still alive, force kill
        if (this.checkProcessHealth(pid)) {
          console.debug(`Process ${pid} still alive, force killing`);
          process.kill(pid, 'SIGKILL');
        }
      }

      // Remove from tracking
      this.unregisterProcess(pid);
      return true;
    } catch (error) {
      console.warn(`Failed to kill process ${pid}:`, error);
      // Still remove from tracking since we tried
      this.unregisterProcess(pid);
      return false;
    }
  }

  /**
   * Kill all tracked processes
   */
  public async killAllProcesses(): Promise<void> {
    console.debug(`Killing all ${this.processes.size} tracked processes`);
    
    const killPromises: Promise<boolean>[] = [];
    for (const processInfo of this.processes.values()) {
      killPromises.push(this.killProcess(processInfo.pid));
    }

    await Promise.allSettled(killPromises);
    this.processes.clear();
  }

  /**
   * Clean up stale processes
   */
  public async killStaleProcesses(timeoutMs: number): Promise<number> {
    const staleProcesses = this.findStaleProcesses(timeoutMs);
    
    if (staleProcesses.length > 0) {
      console.debug(`Found ${staleProcesses.length} stale processes to clean up`);
      
      const killPromises = staleProcesses.map(processInfo => 
        this.killProcess(processInfo.pid)
      );
      
      await Promise.allSettled(killPromises);
    }

    return staleProcesses.length;
  }

  /**
   * Get information about all tracked processes
   */
  public getTrackedProcesses(): ProcessInfo[] {
    return Array.from(this.processes.values());
  }

  /**
   * Get process count
   */
  public getProcessCount(): number {
    return this.processes.size;
  }

  /**
   * Check if running on Windows
   */
  private isWindows(): boolean {
    return process.platform === 'win32';
  }

  /**
   * Kill process on Windows using taskkill
   */
  private async killWindowsProcess(pid: number): Promise<void> {
    const { spawn } = await import('child_process');
    
    return new Promise((resolve, reject) => {
      const taskkill = spawn('taskkill', ['/F', '/PID', pid.toString()]);
      
      taskkill.on('exit', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`taskkill failed with code ${code}`));
        }
      });
      
      taskkill.on('error', reject);
    });
  }

  /**
   * Simple delay utility
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Register signal handlers for cleanup
   */
  private registerSignalHandlers(): void {
    if (this.signalHandlersRegistered) {
      return;
    }

    const cleanup = async () => {
      console.debug('OrphanManager: Cleaning up processes on exit');
      await this.killAllProcesses();
    };

    // Handle various exit scenarios
    process.on('exit', () => {
      // Synchronous cleanup on exit
      for (const processInfo of this.processes.values()) {
        try {
          if (this.isWindows()) {
            // Can't use async taskkill here, try direct kill
            process.kill(processInfo.pid, 'SIGTERM');
          } else {
            process.kill(processInfo.pid, 'SIGKILL');
          }
        } catch (error) {
          console.warn(`Failed to kill process ${processInfo.pid} on exit:`, error);
        }
      }
    });

    process.on('SIGINT', async () => {
      await cleanup();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      await cleanup();
      process.exit(0);
    });

    // Windows specific
    if (this.isWindows()) {
      process.on('SIGBREAK', async () => {
        await cleanup();
        process.exit(0);
      });
    }

    this.signalHandlersRegistered = true;
  }

  /**
   * Start monitoring for stale processes
   */
  private startStaleProcessMonitoring(): void {
    if (this.staleCheckInterval) {
      return;
    }

    this.staleCheckInterval = setInterval(async () => {
      try {
        await this.killStaleProcesses(30000); // 30 seconds timeout
      } catch (error) {
        console.warn('Error during stale process cleanup:', error);
      }
    }, 30000); // Check every 30 seconds

    // Clear interval on exit
    process.on('exit', () => {
      if (this.staleCheckInterval) {
        clearInterval(this.staleCheckInterval);
      }
    });
  }

  /**
   * Stop monitoring for stale processes
   */
  public stopStaleProcessMonitoring(): void {
    if (this.staleCheckInterval) {
      clearInterval(this.staleCheckInterval);
      this.staleCheckInterval = null;
      console.debug('OrphanManager: Stopped stale process monitoring');
    }
  }
}

// Export singleton instance
export const orphanManager = OrphanManager.getInstance();
