{"version": 3, "names": ["_index", "require", "_t", "getAssignmentIdentifiers", "_getAssignmentIdentifiers", "getBindingIdentifiers", "_getBindingIdentifiers", "getOuterBindingIdentifiers", "_getOuterBindingIdentifiers", "numericLiteral", "unaryExpression", "NORMAL_COMPLETION", "BREAK_COMPLETION", "NormalCompletion", "path", "type", "BreakCompletion", "getOpposite", "key", "getSibling", "addCompletionRecords", "records", "context", "push", "_getCompletionRecords", "completionRecordForSwitch", "cases", "lastNormalCompletions", "i", "length", "casePath", "caseCompletions", "normalCompletions", "breakCompletions", "c", "normalCompletionToBreak", "completions", "for<PERSON>ach", "replaceBreakStatementInBreakCompletion", "reachable", "isBreakStatement", "label", "replaceWith", "remove", "getStatementListCompletion", "paths", "canHaveBreak", "newContext", "Object", "assign", "inCaseClause", "isBlockStatement", "shouldPopulateBreak", "statementCompletions", "every", "some", "isDeclaration", "shouldPreserveBreak", "pathCompletions", "isVariableDeclaration", "isEmptyStatement", "isIfStatement", "get", "isDoExpression", "isFor", "<PERSON><PERSON><PERSON><PERSON>", "isLabeledStatement", "isProgram", "isFunction", "isTryStatement", "isCatchClause", "isSwitchStatement", "isSwitchCase", "getCompletionRecords", "map", "r", "NodePath", "parentPath", "parent", "container", "<PERSON><PERSON><PERSON>", "setContext", "getPrevSibling", "getNextSibling", "getAllNextSiblings", "_key", "sibling", "siblings", "node", "getAllPrevSiblings", "parts", "split", "_get<PERSON>ey", "call", "_get<PERSON>attern", "Array", "isArray", "_", "part", "duplicates", "getBindingIdentifierPaths", "outerOnly", "search", "ids", "create", "id", "shift", "keys", "isIdentifier", "_ids", "name", "isExportDeclaration", "declaration", "isFunctionDeclaration", "isFunctionExpression", "child", "getOuterBindingIdentifierPaths"], "sources": ["../../src/path/family.ts"], "sourcesContent": ["// This file contains methods responsible for dealing with/retrieving children or siblings.\n\nimport type TraversalContext from \"../context.ts\";\nimport NodePath from \"./index.ts\";\nimport {\n  getAssignmentIdentifiers as _getAssignmentIdentifiers,\n  getBindingIdentifiers as _getBindingIdentifiers,\n  getOuterBindingIdentifiers as _getOuterBindingIdentifiers,\n  numericLiteral,\n  unaryExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\nconst NORMAL_COMPLETION = 0;\nconst BREAK_COMPLETION = 1;\n\ntype Completion = {\n  path: NodePath;\n  type: 0 | 1;\n};\n\ntype CompletionContext = {\n  // whether the current context allows `break` statement. When it allows, we have\n  // to search all the statements for potential `break`\n  canHaveBreak: boolean;\n  // whether the statement is an immediate descendant of a switch case clause\n  inCaseClause: boolean;\n  // whether the `break` statement record should be populated to upper level\n  // when a `break` statement is an immediate descendant of a block statement, e.g.\n  // `{ break }`, it can influence the control flow in the upper levels.\n  shouldPopulateBreak: boolean;\n  // Whether the `break` statement should be preserved.\n  shouldPreserveBreak: boolean;\n};\n\nfunction NormalCompletion(path: NodePath): Completion {\n  return { type: NORMAL_COMPLETION, path };\n}\n\nfunction BreakCompletion(path: NodePath): Completion {\n  return { type: BREAK_COMPLETION, path };\n}\n\nexport function getOpposite(this: NodePath): NodePath | null {\n  if (this.key === \"left\") {\n    return this.getSibling(\"right\");\n  } else if (this.key === \"right\") {\n    return this.getSibling(\"left\");\n  }\n  return null;\n}\n\nfunction addCompletionRecords(\n  path: NodePath | null | undefined,\n  records: Completion[],\n  context: CompletionContext,\n): Completion[] {\n  if (path) {\n    records.push(..._getCompletionRecords(path, context));\n  }\n  return records;\n}\n\nfunction completionRecordForSwitch(\n  cases: NodePath<t.SwitchCase>[],\n  records: Completion[],\n  context: CompletionContext,\n): Completion[] {\n  // https://tc39.es/ecma262/#sec-runtime-semantics-caseblockevaluation\n  let lastNormalCompletions: Completion[] = [];\n  for (let i = 0; i < cases.length; i++) {\n    const casePath = cases[i];\n    const caseCompletions = _getCompletionRecords(casePath, context);\n    const normalCompletions = [];\n    const breakCompletions = [];\n    for (const c of caseCompletions) {\n      if (c.type === NORMAL_COMPLETION) {\n        normalCompletions.push(c);\n      }\n      if (c.type === BREAK_COMPLETION) {\n        breakCompletions.push(c);\n      }\n    }\n    if (normalCompletions.length) {\n      lastNormalCompletions = normalCompletions;\n    }\n    records.push(...breakCompletions);\n  }\n  records.push(...lastNormalCompletions);\n  return records;\n}\n\nfunction normalCompletionToBreak(completions: Completion[]) {\n  completions.forEach(c => {\n    c.type = BREAK_COMPLETION;\n  });\n}\n\n/**\n * Determine how we should handle the break statement for break completions\n *\n * @param {Completion[]} completions\n * @param {boolean} reachable Whether the break statement is reachable after\n   we mark the normal completions _before_ the given break completions as the final\n   completions. For example,\n   `{ 0 }; break;` is transformed to `{ return 0 }; break;`, the `break` here is unreachable\n   and thus can be removed without consequences. We may in the future reserve them instead since\n   we do not consistently remove unreachable statements _after_ break\n   `{ var x = 0 }; break;` is transformed to `{ var x = 0 }; return void 0;`, the `break` is reachable\n   because we can not wrap variable declaration under a return statement\n */\nfunction replaceBreakStatementInBreakCompletion(\n  completions: Completion[],\n  reachable: boolean,\n) {\n  completions.forEach(c => {\n    if (c.path.isBreakStatement({ label: null })) {\n      if (reachable) {\n        c.path.replaceWith(unaryExpression(\"void\", numericLiteral(0)));\n      } else {\n        c.path.remove();\n      }\n    }\n  });\n}\n\nfunction getStatementListCompletion(\n  paths: NodePath[],\n  context: CompletionContext,\n): Completion[] {\n  const completions = [];\n  if (context.canHaveBreak) {\n    let lastNormalCompletions = [];\n    for (let i = 0; i < paths.length; i++) {\n      const path = paths[i];\n      const newContext = { ...context, inCaseClause: false };\n      if (\n        path.isBlockStatement() &&\n        (context.inCaseClause || // case test: { break }\n          context.shouldPopulateBreak) // case test: { { break } }\n      ) {\n        newContext.shouldPopulateBreak = true;\n      } else {\n        newContext.shouldPopulateBreak = false;\n      }\n      const statementCompletions = _getCompletionRecords(path, newContext);\n      if (\n        statementCompletions.length > 0 &&\n        // we can stop search `paths` when we have seen a `path` that is\n        // effectively a `break` statement. Examples are\n        // - `break`\n        // - `if (true) { 1; break } else { 2; break }`\n        // - `{ break }```\n        // In other words, the paths after this `path` are unreachable\n        statementCompletions.every(c => c.type === BREAK_COMPLETION)\n      ) {\n        if (\n          lastNormalCompletions.length > 0 &&\n          statementCompletions.every(c =>\n            c.path.isBreakStatement({ label: null }),\n          )\n        ) {\n          // when a break completion has a path as BreakStatement, it must be `{ break }`\n          // whose completion value we can not determine, otherwise it would have been\n          // replaced by `replaceBreakStatementInBreakCompletion`\n          // When we have seen normal completions from the last statement\n          // it is safe to stop populating break and mark normal completions as break\n          normalCompletionToBreak(lastNormalCompletions);\n          completions.push(...lastNormalCompletions);\n          // Declarations have empty completion record, however they can not be nested\n          // directly in return statement, i.e. `return (var a = 1)` is invalid.\n          if (lastNormalCompletions.some(c => c.path.isDeclaration())) {\n            completions.push(...statementCompletions);\n            if (!context.shouldPreserveBreak) {\n              replaceBreakStatementInBreakCompletion(\n                statementCompletions,\n                /* reachable */ true,\n              );\n            }\n          }\n          if (!context.shouldPreserveBreak) {\n            replaceBreakStatementInBreakCompletion(\n              statementCompletions,\n              /* reachable */ false,\n            );\n          }\n        } else {\n          completions.push(...statementCompletions);\n          if (!context.shouldPopulateBreak && !context.shouldPreserveBreak) {\n            replaceBreakStatementInBreakCompletion(\n              statementCompletions,\n              /* reachable */ true,\n            );\n          }\n        }\n        break;\n      }\n      if (i === paths.length - 1) {\n        completions.push(...statementCompletions);\n      } else {\n        lastNormalCompletions = [];\n        for (let i = 0; i < statementCompletions.length; i++) {\n          const c = statementCompletions[i];\n          if (c.type === BREAK_COMPLETION) {\n            completions.push(c);\n          }\n          if (c.type === NORMAL_COMPLETION) {\n            lastNormalCompletions.push(c);\n          }\n        }\n      }\n    }\n  } else if (paths.length) {\n    // When we are in a context where `break` must not exist, we can skip linear\n    // search on statement lists and assume that the last\n    // non-variable-declaration statement determines the completion.\n    for (let i = paths.length - 1; i >= 0; i--) {\n      const pathCompletions = _getCompletionRecords(paths[i], context);\n      if (\n        pathCompletions.length > 1 ||\n        (pathCompletions.length === 1 &&\n          !pathCompletions[0].path.isVariableDeclaration() &&\n          !pathCompletions[0].path.isEmptyStatement())\n      ) {\n        completions.push(...pathCompletions);\n        break;\n      }\n    }\n  }\n  return completions;\n}\n\nfunction _getCompletionRecords(\n  path: NodePath,\n  context: CompletionContext,\n): Completion[] {\n  let records: Completion[] = [];\n  if (path.isIfStatement()) {\n    records = addCompletionRecords(path.get(\"consequent\"), records, context);\n    records = addCompletionRecords(path.get(\"alternate\"), records, context);\n  } else if (\n    path.isDoExpression() ||\n    path.isFor() ||\n    path.isWhile() ||\n    path.isLabeledStatement()\n  ) {\n    return addCompletionRecords(path.get(\"body\"), records, context);\n  } else if (path.isProgram() || path.isBlockStatement()) {\n    return getStatementListCompletion(path.get(\"body\"), context);\n  } else if (path.isFunction()) {\n    return _getCompletionRecords(path.get(\"body\"), context);\n  } else if (path.isTryStatement()) {\n    records = addCompletionRecords(path.get(\"block\"), records, context);\n    records = addCompletionRecords(path.get(\"handler\"), records, context);\n  } else if (path.isCatchClause()) {\n    return addCompletionRecords(path.get(\"body\"), records, context);\n  } else if (path.isSwitchStatement()) {\n    return completionRecordForSwitch(path.get(\"cases\"), records, context);\n  } else if (path.isSwitchCase()) {\n    return getStatementListCompletion(path.get(\"consequent\"), {\n      canHaveBreak: true,\n      shouldPopulateBreak: false,\n      inCaseClause: true,\n      shouldPreserveBreak: context.shouldPreserveBreak,\n    });\n  } else if (path.isBreakStatement()) {\n    records.push(BreakCompletion(path));\n  } else {\n    records.push(NormalCompletion(path));\n  }\n\n  return records;\n}\n\n/**\n * Retrieve the completion records of a given path.\n * Note: to ensure proper support on `break` statement, this method\n * will manipulate the AST around the break statement. Do not call the method\n * twice for the same path.\n *\n * @export\n * @param {NodePath} this\n * @param {boolean} [shouldPreserveBreak=false] Whether the `break` statement should be preserved.\n * @returns {NodePath[]} Completion records\n */\nexport function getCompletionRecords(\n  this: NodePath,\n  shouldPreserveBreak = false,\n): NodePath[] {\n  const records = _getCompletionRecords(this, {\n    canHaveBreak: false,\n    shouldPopulateBreak: false,\n    inCaseClause: false,\n    shouldPreserveBreak,\n  });\n  return records.map(r => r.path);\n}\n\nexport function getSibling(this: NodePath, key: string | number): NodePath {\n  return NodePath.get({\n    parentPath: this.parentPath,\n    parent: this.parent,\n    container: this.container,\n    listKey: this.listKey,\n    key: key,\n  }).setContext(this.context);\n}\n\nexport function getPrevSibling(this: NodePath): NodePath {\n  // @ts-expect-error todo(flow->ts) this.key could be a string\n  return this.getSibling(this.key - 1);\n}\n\nexport function getNextSibling(this: NodePath): NodePath {\n  // @ts-expect-error todo(flow->ts) this.key could be a string\n  return this.getSibling(this.key + 1);\n}\n\nexport function getAllNextSiblings(this: NodePath): NodePath[] {\n  // @ts-expect-error todo(flow->ts) this.key could be a string\n  let _key: number = this.key;\n  let sibling = this.getSibling(++_key);\n  const siblings = [];\n  while (sibling.node) {\n    siblings.push(sibling);\n    sibling = this.getSibling(++_key);\n  }\n  return siblings;\n}\n\nexport function getAllPrevSiblings(this: NodePath): NodePath[] {\n  // @ts-expect-error todo(flow->ts) this.key could be a string\n  let _key: number = this.key;\n  let sibling = this.getSibling(--_key);\n  const siblings = [];\n  while (sibling.node) {\n    siblings.push(sibling);\n    sibling = this.getSibling(--_key);\n  }\n  return siblings;\n}\n\n// convert \"1\" to 1 (string index to number index)\ntype MaybeToIndex<T extends string> = T extends `${bigint}` ? number : T;\n\ntype Pattern<Obj extends string, Prop extends string> = `${Obj}.${Prop}`;\n\n// split \"body.body.1\" to [\"body\", \"body\", 1]\ntype Split<P extends string> =\n  P extends Pattern<infer O, infer U>\n    ? [MaybeToIndex<O>, ...Split<U>]\n    : [MaybeToIndex<P>];\n\n// traverse the Node with tuple path [\"body\", \"body\", 1]\n// Path should be created with Split\ntype Trav<\n  Node extends t.Node | t.Node[],\n  Path extends unknown[],\n> = Path extends [infer K, ...infer R]\n  ? K extends keyof Node\n    ? Node[K] extends t.Node | t.Node[]\n      ? R extends []\n        ? Node[K]\n        : Trav<Node[K], R>\n      : never\n    : never\n  : never;\n\ntype ToNodePath<T> =\n  T extends Array<t.Node | null | undefined>\n    ? Array<NodePath<T[number]>>\n    : T extends t.Node | null | undefined\n      ? NodePath<T>\n      : never;\n\nfunction get<T extends NodePath, K extends keyof T[\"node\"]>(\n  this: T,\n  key: K,\n  context?: boolean | TraversalContext,\n): T extends any\n  ? T[\"node\"][K] extends Array<t.Node | null | undefined>\n    ? Array<NodePath<T[\"node\"][K][number]>>\n    : T[\"node\"][K] extends t.Node | null | undefined\n      ? NodePath<T[\"node\"][K]>\n      : never\n  : never;\n\nfunction get<T extends NodePath, K extends string>(\n  this: T,\n  key: K,\n  context?: boolean | TraversalContext,\n): T extends any ? ToNodePath<Trav<T[\"node\"], Split<K>>> : never;\n\nfunction get(\n  this: NodePath,\n  key: string,\n  context?: true | TraversalContext,\n): NodePath | NodePath[];\n\nfunction get(\n  this: NodePath,\n  key: string,\n  context: true | TraversalContext = true,\n): NodePath | NodePath[] {\n  if (context === true) context = this.context;\n  const parts = key.split(\".\");\n  if (parts.length === 1) {\n    // \"foo\"\n    // @ts-expect-error key may not index T\n    return _getKey.call(this, key, context);\n  } else {\n    // \"foo.bar\"\n    return _getPattern.call(this, parts, context);\n  }\n}\n\nexport { get };\n\nexport function _getKey<T extends t.Node>(\n  this: NodePath<T>,\n  key: keyof T & string,\n  context?: TraversalContext,\n): NodePath | NodePath[] {\n  const node = this.node as T;\n  const container = node[key];\n\n  if (Array.isArray(container)) {\n    // requested a container so give them all the paths\n    return container.map((_, i) => {\n      return NodePath.get({\n        listKey: key,\n        parentPath: this,\n        parent: node,\n        container: container,\n        key: i,\n      }).setContext(context);\n    });\n  } else {\n    return NodePath.get({\n      parentPath: this,\n      parent: node,\n      container: node,\n      key: key,\n    }).setContext(context);\n  }\n}\n\nexport function _getPattern(\n  this: NodePath,\n  parts: string[],\n  context?: TraversalContext,\n): NodePath | NodePath[] {\n  let path: NodePath | NodePath[] = this;\n  for (const part of parts) {\n    if (part === \".\") {\n      // @ts-expect-error todo(flow-ts): Can path be an array here?\n      path = path.parentPath;\n    } else {\n      if (Array.isArray(path)) {\n        // @ts-expect-error part may not index path\n        path = path[part];\n      } else {\n        path = path.get(part, context);\n      }\n    }\n  }\n  return path;\n}\n\nexport function getAssignmentIdentifiers(this: NodePath) {\n  return _getAssignmentIdentifiers(this.node);\n}\n\nfunction getBindingIdentifiers(\n  duplicates: true,\n): Record<string, t.Identifier[]>;\nfunction getBindingIdentifiers(\n  duplicates?: false,\n): Record<string, t.Identifier>;\nfunction getBindingIdentifiers(\n  duplicates: boolean,\n): Record<string, t.Identifier[] | t.Identifier>;\n\nfunction getBindingIdentifiers(\n  this: NodePath,\n  duplicates?: boolean,\n): Record<string, t.Identifier[] | t.Identifier> {\n  return _getBindingIdentifiers(this.node, duplicates);\n}\n\nexport { getBindingIdentifiers };\n\nfunction getOuterBindingIdentifiers(\n  duplicates: true,\n): Record<string, t.Identifier[]>;\nfunction getOuterBindingIdentifiers(\n  duplicates?: false,\n): Record<string, t.Identifier>;\nfunction getOuterBindingIdentifiers(\n  duplicates: boolean,\n): Record<string, t.Identifier[] | t.Identifier>;\n\nfunction getOuterBindingIdentifiers(\n  this: NodePath,\n  duplicates?: boolean,\n): Record<string, t.Identifier[] | t.Identifier> {\n  return _getOuterBindingIdentifiers(this.node, duplicates);\n}\n\nexport { getOuterBindingIdentifiers };\n\nfunction getBindingIdentifierPaths(\n  duplicates: true,\n  outerOnly?: boolean,\n): Record<string, NodePath<t.Identifier>[]>;\nfunction getBindingIdentifierPaths(\n  duplicates: false,\n  outerOnly?: boolean,\n): Record<string, NodePath<t.Identifier>>;\nfunction getBindingIdentifierPaths(\n  duplicates?: boolean,\n  outerOnly?: boolean,\n): Record<string, NodePath<t.Identifier> | NodePath<t.Identifier>[]>;\n\n// original source - https://github.com/babel/babel/blob/main/packages/babel-types/src/retrievers/getBindingIdentifiers.js\n// path.getBindingIdentifiers returns nodes where the following re-implementation returns paths\nfunction getBindingIdentifierPaths(\n  this: NodePath,\n  duplicates: boolean = false,\n  outerOnly: boolean = false,\n): Record<string, NodePath<t.Identifier> | NodePath<t.Identifier>[]> {\n  const path = this;\n  const search = [path];\n  const ids = Object.create(null);\n\n  while (search.length) {\n    const id = search.shift();\n    if (!id) continue;\n    if (!id.node) continue;\n\n    const keys = _getBindingIdentifiers.keys[id.node.type];\n\n    if (id.isIdentifier()) {\n      if (duplicates) {\n        const _ids = (ids[id.node.name] = ids[id.node.name] || []);\n        _ids.push(id);\n      } else {\n        ids[id.node.name] = id;\n      }\n      continue;\n    }\n\n    if (id.isExportDeclaration()) {\n      const declaration = id.get(\"declaration\");\n      if (declaration.isDeclaration()) {\n        search.push(declaration);\n      }\n      continue;\n    }\n\n    if (outerOnly) {\n      if (id.isFunctionDeclaration()) {\n        search.push(id.get(\"id\"));\n        continue;\n      }\n      if (id.isFunctionExpression()) {\n        continue;\n      }\n    }\n\n    if (keys) {\n      for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        const child = id.get(key);\n        if (Array.isArray(child)) {\n          search.push(...child);\n        } else if (child.node) {\n          search.push(child);\n        }\n      }\n    }\n  }\n\n  return ids;\n}\n\nexport { getBindingIdentifierPaths };\n\nfunction getOuterBindingIdentifierPaths(\n  duplicates: true,\n): Record<string, NodePath<t.Identifier>[]>;\nfunction getOuterBindingIdentifierPaths(\n  duplicates?: false,\n): Record<string, NodePath<t.Identifier>>;\nfunction getOuterBindingIdentifierPaths(\n  duplicates?: boolean,\n): Record<string, NodePath<t.Identifier> | NodePath<t.Identifier>[]>;\n\nfunction getOuterBindingIdentifierPaths(\n  this: NodePath,\n  duplicates: boolean = false,\n) {\n  return this.getBindingIdentifierPaths(duplicates, true);\n}\n\nexport { getOuterBindingIdentifierPaths };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,EAAA,GAAAD,OAAA;AAMsB;EALpBE,wBAAwB,EAAIC,yBAAyB;EACrDC,qBAAqB,EAAIC,sBAAsB;EAC/CC,0BAA0B,EAAIC,2BAA2B;EACzDC,cAAc;EACdC;AAAe,IAAAR,EAAA;AAIjB,MAAMS,iBAAiB,GAAG,CAAC;AAC3B,MAAMC,gBAAgB,GAAG,CAAC;AAqB1B,SAASC,gBAAgBA,CAACC,IAAc,EAAc;EACpD,OAAO;IAAEC,IAAI,EAAEJ,iBAAiB;IAAEG;EAAK,CAAC;AAC1C;AAEA,SAASE,eAAeA,CAACF,IAAc,EAAc;EACnD,OAAO;IAAEC,IAAI,EAAEH,gBAAgB;IAAEE;EAAK,CAAC;AACzC;AAEO,SAASG,WAAWA,CAAA,EAAkC;EAC3D,IAAI,IAAI,CAACC,GAAG,KAAK,MAAM,EAAE;IACvB,OAAO,IAAI,CAACC,UAAU,CAAC,OAAO,CAAC;EACjC,CAAC,MAAM,IAAI,IAAI,CAACD,GAAG,KAAK,OAAO,EAAE;IAC/B,OAAO,IAAI,CAACC,UAAU,CAAC,MAAM,CAAC;EAChC;EACA,OAAO,IAAI;AACb;AAEA,SAASC,oBAAoBA,CAC3BN,IAAiC,EACjCO,OAAqB,EACrBC,OAA0B,EACZ;EACd,IAAIR,IAAI,EAAE;IACRO,OAAO,CAACE,IAAI,CAAC,GAAGC,qBAAqB,CAACV,IAAI,EAAEQ,OAAO,CAAC,CAAC;EACvD;EACA,OAAOD,OAAO;AAChB;AAEA,SAASI,yBAAyBA,CAChCC,KAA+B,EAC/BL,OAAqB,EACrBC,OAA0B,EACZ;EAEd,IAAIK,qBAAmC,GAAG,EAAE;EAC5C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAME,QAAQ,GAAGJ,KAAK,CAACE,CAAC,CAAC;IACzB,MAAMG,eAAe,GAAGP,qBAAqB,CAACM,QAAQ,EAAER,OAAO,CAAC;IAChE,MAAMU,iBAAiB,GAAG,EAAE;IAC5B,MAAMC,gBAAgB,GAAG,EAAE;IAC3B,KAAK,MAAMC,CAAC,IAAIH,eAAe,EAAE;MAC/B,IAAIG,CAAC,CAACnB,IAAI,KAAKJ,iBAAiB,EAAE;QAChCqB,iBAAiB,CAACT,IAAI,CAACW,CAAC,CAAC;MAC3B;MACA,IAAIA,CAAC,CAACnB,IAAI,KAAKH,gBAAgB,EAAE;QAC/BqB,gBAAgB,CAACV,IAAI,CAACW,CAAC,CAAC;MAC1B;IACF;IACA,IAAIF,iBAAiB,CAACH,MAAM,EAAE;MAC5BF,qBAAqB,GAAGK,iBAAiB;IAC3C;IACAX,OAAO,CAACE,IAAI,CAAC,GAAGU,gBAAgB,CAAC;EACnC;EACAZ,OAAO,CAACE,IAAI,CAAC,GAAGI,qBAAqB,CAAC;EACtC,OAAON,OAAO;AAChB;AAEA,SAASc,uBAAuBA,CAACC,WAAyB,EAAE;EAC1DA,WAAW,CAACC,OAAO,CAACH,CAAC,IAAI;IACvBA,CAAC,CAACnB,IAAI,GAAGH,gBAAgB;EAC3B,CAAC,CAAC;AACJ;AAeA,SAAS0B,sCAAsCA,CAC7CF,WAAyB,EACzBG,SAAkB,EAClB;EACAH,WAAW,CAACC,OAAO,CAACH,CAAC,IAAI;IACvB,IAAIA,CAAC,CAACpB,IAAI,CAAC0B,gBAAgB,CAAC;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC,EAAE;MAC5C,IAAIF,SAAS,EAAE;QACbL,CAAC,CAACpB,IAAI,CAAC4B,WAAW,CAAChC,eAAe,CAAC,MAAM,EAAED,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MAChE,CAAC,MAAM;QACLyB,CAAC,CAACpB,IAAI,CAAC6B,MAAM,CAAC,CAAC;MACjB;IACF;EACF,CAAC,CAAC;AACJ;AAEA,SAASC,0BAA0BA,CACjCC,KAAiB,EACjBvB,OAA0B,EACZ;EACd,MAAMc,WAAW,GAAG,EAAE;EACtB,IAAId,OAAO,CAACwB,YAAY,EAAE;IACxB,IAAInB,qBAAqB,GAAG,EAAE;IAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,KAAK,CAAChB,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,MAAMd,IAAI,GAAG+B,KAAK,CAACjB,CAAC,CAAC;MACrB,MAAMmB,UAAU,GAAAC,MAAA,CAAAC,MAAA,KAAQ3B,OAAO;QAAE4B,YAAY,EAAE;MAAK,EAAE;MACtD,IACEpC,IAAI,CAACqC,gBAAgB,CAAC,CAAC,KACtB7B,OAAO,CAAC4B,YAAY,IACnB5B,OAAO,CAAC8B,mBAAmB,CAAC,EAC9B;QACAL,UAAU,CAACK,mBAAmB,GAAG,IAAI;MACvC,CAAC,MAAM;QACLL,UAAU,CAACK,mBAAmB,GAAG,KAAK;MACxC;MACA,MAAMC,oBAAoB,GAAG7B,qBAAqB,CAACV,IAAI,EAAEiC,UAAU,CAAC;MACpE,IACEM,oBAAoB,CAACxB,MAAM,GAAG,CAAC,IAO/BwB,oBAAoB,CAACC,KAAK,CAACpB,CAAC,IAAIA,CAAC,CAACnB,IAAI,KAAKH,gBAAgB,CAAC,EAC5D;QACA,IACEe,qBAAqB,CAACE,MAAM,GAAG,CAAC,IAChCwB,oBAAoB,CAACC,KAAK,CAACpB,CAAC,IAC1BA,CAAC,CAACpB,IAAI,CAAC0B,gBAAgB,CAAC;UAAEC,KAAK,EAAE;QAAK,CAAC,CACzC,CAAC,EACD;UAMAN,uBAAuB,CAACR,qBAAqB,CAAC;UAC9CS,WAAW,CAACb,IAAI,CAAC,GAAGI,qBAAqB,CAAC;UAG1C,IAAIA,qBAAqB,CAAC4B,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAACpB,IAAI,CAAC0C,aAAa,CAAC,CAAC,CAAC,EAAE;YAC3DpB,WAAW,CAACb,IAAI,CAAC,GAAG8B,oBAAoB,CAAC;YACzC,IAAI,CAAC/B,OAAO,CAACmC,mBAAmB,EAAE;cAChCnB,sCAAsC,CACpCe,oBAAoB,EACJ,IAClB,CAAC;YACH;UACF;UACA,IAAI,CAAC/B,OAAO,CAACmC,mBAAmB,EAAE;YAChCnB,sCAAsC,CACpCe,oBAAoB,EACJ,KAClB,CAAC;UACH;QACF,CAAC,MAAM;UACLjB,WAAW,CAACb,IAAI,CAAC,GAAG8B,oBAAoB,CAAC;UACzC,IAAI,CAAC/B,OAAO,CAAC8B,mBAAmB,IAAI,CAAC9B,OAAO,CAACmC,mBAAmB,EAAE;YAChEnB,sCAAsC,CACpCe,oBAAoB,EACJ,IAClB,CAAC;UACH;QACF;QACA;MACF;MACA,IAAIzB,CAAC,KAAKiB,KAAK,CAAChB,MAAM,GAAG,CAAC,EAAE;QAC1BO,WAAW,CAACb,IAAI,CAAC,GAAG8B,oBAAoB,CAAC;MAC3C,CAAC,MAAM;QACL1B,qBAAqB,GAAG,EAAE;QAC1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,oBAAoB,CAACxB,MAAM,EAAED,CAAC,EAAE,EAAE;UACpD,MAAMM,CAAC,GAAGmB,oBAAoB,CAACzB,CAAC,CAAC;UACjC,IAAIM,CAAC,CAACnB,IAAI,KAAKH,gBAAgB,EAAE;YAC/BwB,WAAW,CAACb,IAAI,CAACW,CAAC,CAAC;UACrB;UACA,IAAIA,CAAC,CAACnB,IAAI,KAAKJ,iBAAiB,EAAE;YAChCgB,qBAAqB,CAACJ,IAAI,CAACW,CAAC,CAAC;UAC/B;QACF;MACF;IACF;EACF,CAAC,MAAM,IAAIW,KAAK,CAAChB,MAAM,EAAE;IAIvB,KAAK,IAAID,CAAC,GAAGiB,KAAK,CAAChB,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1C,MAAM8B,eAAe,GAAGlC,qBAAqB,CAACqB,KAAK,CAACjB,CAAC,CAAC,EAAEN,OAAO,CAAC;MAChE,IACEoC,eAAe,CAAC7B,MAAM,GAAG,CAAC,IACzB6B,eAAe,CAAC7B,MAAM,KAAK,CAAC,IAC3B,CAAC6B,eAAe,CAAC,CAAC,CAAC,CAAC5C,IAAI,CAAC6C,qBAAqB,CAAC,CAAC,IAChD,CAACD,eAAe,CAAC,CAAC,CAAC,CAAC5C,IAAI,CAAC8C,gBAAgB,CAAC,CAAE,EAC9C;QACAxB,WAAW,CAACb,IAAI,CAAC,GAAGmC,eAAe,CAAC;QACpC;MACF;IACF;EACF;EACA,OAAOtB,WAAW;AACpB;AAEA,SAASZ,qBAAqBA,CAC5BV,IAAc,EACdQ,OAA0B,EACZ;EACd,IAAID,OAAqB,GAAG,EAAE;EAC9B,IAAIP,IAAI,CAAC+C,aAAa,CAAC,CAAC,EAAE;IACxBxC,OAAO,GAAGD,oBAAoB,CAACN,IAAI,CAACgD,GAAG,CAAC,YAAY,CAAC,EAAEzC,OAAO,EAAEC,OAAO,CAAC;IACxED,OAAO,GAAGD,oBAAoB,CAACN,IAAI,CAACgD,GAAG,CAAC,WAAW,CAAC,EAAEzC,OAAO,EAAEC,OAAO,CAAC;EACzE,CAAC,MAAM,IACLR,IAAI,CAACiD,cAAc,CAAC,CAAC,IACrBjD,IAAI,CAACkD,KAAK,CAAC,CAAC,IACZlD,IAAI,CAACmD,OAAO,CAAC,CAAC,IACdnD,IAAI,CAACoD,kBAAkB,CAAC,CAAC,EACzB;IACA,OAAO9C,oBAAoB,CAACN,IAAI,CAACgD,GAAG,CAAC,MAAM,CAAC,EAAEzC,OAAO,EAAEC,OAAO,CAAC;EACjE,CAAC,MAAM,IAAIR,IAAI,CAACqD,SAAS,CAAC,CAAC,IAAIrD,IAAI,CAACqC,gBAAgB,CAAC,CAAC,EAAE;IACtD,OAAOP,0BAA0B,CAAC9B,IAAI,CAACgD,GAAG,CAAC,MAAM,CAAC,EAAExC,OAAO,CAAC;EAC9D,CAAC,MAAM,IAAIR,IAAI,CAACsD,UAAU,CAAC,CAAC,EAAE;IAC5B,OAAO5C,qBAAqB,CAACV,IAAI,CAACgD,GAAG,CAAC,MAAM,CAAC,EAAExC,OAAO,CAAC;EACzD,CAAC,MAAM,IAAIR,IAAI,CAACuD,cAAc,CAAC,CAAC,EAAE;IAChChD,OAAO,GAAGD,oBAAoB,CAACN,IAAI,CAACgD,GAAG,CAAC,OAAO,CAAC,EAAEzC,OAAO,EAAEC,OAAO,CAAC;IACnED,OAAO,GAAGD,oBAAoB,CAACN,IAAI,CAACgD,GAAG,CAAC,SAAS,CAAC,EAAEzC,OAAO,EAAEC,OAAO,CAAC;EACvE,CAAC,MAAM,IAAIR,IAAI,CAACwD,aAAa,CAAC,CAAC,EAAE;IAC/B,OAAOlD,oBAAoB,CAACN,IAAI,CAACgD,GAAG,CAAC,MAAM,CAAC,EAAEzC,OAAO,EAAEC,OAAO,CAAC;EACjE,CAAC,MAAM,IAAIR,IAAI,CAACyD,iBAAiB,CAAC,CAAC,EAAE;IACnC,OAAO9C,yBAAyB,CAACX,IAAI,CAACgD,GAAG,CAAC,OAAO,CAAC,EAAEzC,OAAO,EAAEC,OAAO,CAAC;EACvE,CAAC,MAAM,IAAIR,IAAI,CAAC0D,YAAY,CAAC,CAAC,EAAE;IAC9B,OAAO5B,0BAA0B,CAAC9B,IAAI,CAACgD,GAAG,CAAC,YAAY,CAAC,EAAE;MACxDhB,YAAY,EAAE,IAAI;MAClBM,mBAAmB,EAAE,KAAK;MAC1BF,YAAY,EAAE,IAAI;MAClBO,mBAAmB,EAAEnC,OAAO,CAACmC;IAC/B,CAAC,CAAC;EACJ,CAAC,MAAM,IAAI3C,IAAI,CAAC0B,gBAAgB,CAAC,CAAC,EAAE;IAClCnB,OAAO,CAACE,IAAI,CAACP,eAAe,CAACF,IAAI,CAAC,CAAC;EACrC,CAAC,MAAM;IACLO,OAAO,CAACE,IAAI,CAACV,gBAAgB,CAACC,IAAI,CAAC,CAAC;EACtC;EAEA,OAAOO,OAAO;AAChB;AAaO,SAASoD,oBAAoBA,CAElChB,mBAAmB,GAAG,KAAK,EACf;EACZ,MAAMpC,OAAO,GAAGG,qBAAqB,CAAC,IAAI,EAAE;IAC1CsB,YAAY,EAAE,KAAK;IACnBM,mBAAmB,EAAE,KAAK;IAC1BF,YAAY,EAAE,KAAK;IACnBO;EACF,CAAC,CAAC;EACF,OAAOpC,OAAO,CAACqD,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC7D,IAAI,CAAC;AACjC;AAEO,SAASK,UAAUA,CAAiBD,GAAoB,EAAY;EACzE,OAAO0D,cAAQ,CAACd,GAAG,CAAC;IAClBe,UAAU,EAAE,IAAI,CAACA,UAAU;IAC3BC,MAAM,EAAE,IAAI,CAACA,MAAM;IACnBC,SAAS,EAAE,IAAI,CAACA,SAAS;IACzBC,OAAO,EAAE,IAAI,CAACA,OAAO;IACrB9D,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC+D,UAAU,CAAC,IAAI,CAAC3D,OAAO,CAAC;AAC7B;AAEO,SAAS4D,cAAcA,CAAA,EAA2B;EAEvD,OAAO,IAAI,CAAC/D,UAAU,CAAC,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC;AACtC;AAEO,SAASiE,cAAcA,CAAA,EAA2B;EAEvD,OAAO,IAAI,CAAChE,UAAU,CAAC,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC;AACtC;AAEO,SAASkE,kBAAkBA,CAAA,EAA6B;EAE7D,IAAIC,IAAY,GAAG,IAAI,CAACnE,GAAG;EAC3B,IAAIoE,OAAO,GAAG,IAAI,CAACnE,UAAU,CAAC,EAAEkE,IAAI,CAAC;EACrC,MAAME,QAAQ,GAAG,EAAE;EACnB,OAAOD,OAAO,CAACE,IAAI,EAAE;IACnBD,QAAQ,CAAChE,IAAI,CAAC+D,OAAO,CAAC;IACtBA,OAAO,GAAG,IAAI,CAACnE,UAAU,CAAC,EAAEkE,IAAI,CAAC;EACnC;EACA,OAAOE,QAAQ;AACjB;AAEO,SAASE,kBAAkBA,CAAA,EAA6B;EAE7D,IAAIJ,IAAY,GAAG,IAAI,CAACnE,GAAG;EAC3B,IAAIoE,OAAO,GAAG,IAAI,CAACnE,UAAU,CAAC,EAAEkE,IAAI,CAAC;EACrC,MAAME,QAAQ,GAAG,EAAE;EACnB,OAAOD,OAAO,CAACE,IAAI,EAAE;IACnBD,QAAQ,CAAChE,IAAI,CAAC+D,OAAO,CAAC;IACtBA,OAAO,GAAG,IAAI,CAACnE,UAAU,CAAC,EAAEkE,IAAI,CAAC;EACnC;EACA,OAAOE,QAAQ;AACjB;AA2DA,SAASzB,GAAGA,CAEV5C,GAAW,EACXI,OAAgC,GAAG,IAAI,EAChB;EACvB,IAAIA,OAAO,KAAK,IAAI,EAAEA,OAAO,GAAG,IAAI,CAACA,OAAO;EAC5C,MAAMoE,KAAK,GAAGxE,GAAG,CAACyE,KAAK,CAAC,GAAG,CAAC;EAC5B,IAAID,KAAK,CAAC7D,MAAM,KAAK,CAAC,EAAE;IAGtB,OAAO+D,OAAO,CAACC,IAAI,CAAC,IAAI,EAAE3E,GAAG,EAAEI,OAAO,CAAC;EACzC,CAAC,MAAM;IAEL,OAAOwE,WAAW,CAACD,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEpE,OAAO,CAAC;EAC/C;AACF;AAIO,SAASsE,OAAOA,CAErB1E,GAAqB,EACrBI,OAA0B,EACH;EACvB,MAAMkE,IAAI,GAAG,IAAI,CAACA,IAAS;EAC3B,MAAMT,SAAS,GAAGS,IAAI,CAACtE,GAAG,CAAC;EAE3B,IAAI6E,KAAK,CAACC,OAAO,CAACjB,SAAS,CAAC,EAAE;IAE5B,OAAOA,SAAS,CAACL,GAAG,CAAC,CAACuB,CAAC,EAAErE,CAAC,KAAK;MAC7B,OAAOgD,cAAQ,CAACd,GAAG,CAAC;QAClBkB,OAAO,EAAE9D,GAAG;QACZ2D,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAEU,IAAI;QACZT,SAAS,EAAEA,SAAS;QACpB7D,GAAG,EAAEU;MACP,CAAC,CAAC,CAACqD,UAAU,CAAC3D,OAAO,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,OAAOsD,cAAQ,CAACd,GAAG,CAAC;MAClBe,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAEU,IAAI;MACZT,SAAS,EAAES,IAAI;MACftE,GAAG,EAAEA;IACP,CAAC,CAAC,CAAC+D,UAAU,CAAC3D,OAAO,CAAC;EACxB;AACF;AAEO,SAASwE,WAAWA,CAEzBJ,KAAe,EACfpE,OAA0B,EACH;EACvB,IAAIR,IAA2B,GAAG,IAAI;EACtC,KAAK,MAAMoF,IAAI,IAAIR,KAAK,EAAE;IACxB,IAAIQ,IAAI,KAAK,GAAG,EAAE;MAEhBpF,IAAI,GAAGA,IAAI,CAAC+D,UAAU;IACxB,CAAC,MAAM;MACL,IAAIkB,KAAK,CAACC,OAAO,CAAClF,IAAI,CAAC,EAAE;QAEvBA,IAAI,GAAGA,IAAI,CAACoF,IAAI,CAAC;MACnB,CAAC,MAAM;QACLpF,IAAI,GAAGA,IAAI,CAACgD,GAAG,CAACoC,IAAI,EAAE5E,OAAO,CAAC;MAChC;IACF;EACF;EACA,OAAOR,IAAI;AACb;AAEO,SAASX,wBAAwBA,CAAA,EAAiB;EACvD,OAAOC,yBAAyB,CAAC,IAAI,CAACoF,IAAI,CAAC;AAC7C;AAYA,SAASnF,qBAAqBA,CAE5B8F,UAAoB,EAC2B;EAC/C,OAAO7F,sBAAsB,CAAC,IAAI,CAACkF,IAAI,EAAEW,UAAU,CAAC;AACtD;AAcA,SAAS5F,0BAA0BA,CAEjC4F,UAAoB,EAC2B;EAC/C,OAAO3F,2BAA2B,CAAC,IAAI,CAACgF,IAAI,EAAEW,UAAU,CAAC;AAC3D;AAmBA,SAASC,yBAAyBA,CAEhCD,UAAmB,GAAG,KAAK,EAC3BE,SAAkB,GAAG,KAAK,EACyC;EACnE,MAAMvF,IAAI,GAAG,IAAI;EACjB,MAAMwF,MAAM,GAAG,CAACxF,IAAI,CAAC;EACrB,MAAMyF,GAAG,GAAGvD,MAAM,CAACwD,MAAM,CAAC,IAAI,CAAC;EAE/B,OAAOF,MAAM,CAACzE,MAAM,EAAE;IACpB,MAAM4E,EAAE,GAAGH,MAAM,CAACI,KAAK,CAAC,CAAC;IACzB,IAAI,CAACD,EAAE,EAAE;IACT,IAAI,CAACA,EAAE,CAACjB,IAAI,EAAE;IAEd,MAAMmB,IAAI,GAAGrG,sBAAsB,CAACqG,IAAI,CAACF,EAAE,CAACjB,IAAI,CAACzE,IAAI,CAAC;IAEtD,IAAI0F,EAAE,CAACG,YAAY,CAAC,CAAC,EAAE;MACrB,IAAIT,UAAU,EAAE;QACd,MAAMU,IAAI,GAAIN,GAAG,CAACE,EAAE,CAACjB,IAAI,CAACsB,IAAI,CAAC,GAAGP,GAAG,CAACE,EAAE,CAACjB,IAAI,CAACsB,IAAI,CAAC,IAAI,EAAG;QAC1DD,IAAI,CAACtF,IAAI,CAACkF,EAAE,CAAC;MACf,CAAC,MAAM;QACLF,GAAG,CAACE,EAAE,CAACjB,IAAI,CAACsB,IAAI,CAAC,GAAGL,EAAE;MACxB;MACA;IACF;IAEA,IAAIA,EAAE,CAACM,mBAAmB,CAAC,CAAC,EAAE;MAC5B,MAAMC,WAAW,GAAGP,EAAE,CAAC3C,GAAG,CAAC,aAAa,CAAC;MACzC,IAAIkD,WAAW,CAACxD,aAAa,CAAC,CAAC,EAAE;QAC/B8C,MAAM,CAAC/E,IAAI,CAACyF,WAAW,CAAC;MAC1B;MACA;IACF;IAEA,IAAIX,SAAS,EAAE;MACb,IAAII,EAAE,CAACQ,qBAAqB,CAAC,CAAC,EAAE;QAC9BX,MAAM,CAAC/E,IAAI,CAACkF,EAAE,CAAC3C,GAAG,CAAC,IAAI,CAAC,CAAC;QACzB;MACF;MACA,IAAI2C,EAAE,CAACS,oBAAoB,CAAC,CAAC,EAAE;QAC7B;MACF;IACF;IAEA,IAAIP,IAAI,EAAE;MACR,KAAK,IAAI/E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+E,IAAI,CAAC9E,MAAM,EAAED,CAAC,EAAE,EAAE;QACpC,MAAMV,GAAG,GAAGyF,IAAI,CAAC/E,CAAC,CAAC;QACnB,MAAMuF,KAAK,GAAGV,EAAE,CAAC3C,GAAG,CAAC5C,GAAG,CAAC;QACzB,IAAI6E,KAAK,CAACC,OAAO,CAACmB,KAAK,CAAC,EAAE;UACxBb,MAAM,CAAC/E,IAAI,CAAC,GAAG4F,KAAK,CAAC;QACvB,CAAC,MAAM,IAAIA,KAAK,CAAC3B,IAAI,EAAE;UACrBc,MAAM,CAAC/E,IAAI,CAAC4F,KAAK,CAAC;QACpB;MACF;IACF;EACF;EAEA,OAAOZ,GAAG;AACZ;AAcA,SAASa,8BAA8BA,CAErCjB,UAAmB,GAAG,KAAK,EAC3B;EACA,OAAO,IAAI,CAACC,yBAAyB,CAACD,UAAU,EAAE,IAAI,CAAC;AACzD", "ignoreList": []}