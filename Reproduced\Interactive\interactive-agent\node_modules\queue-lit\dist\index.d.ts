/**
 * Queue
 * @implements Iterable<Value>
 * @template Value
 */
export class Queue<Value> implements Iterable<Value> {
    /**
     * clear
     * @returns {void}
     */
    clear(): void;
    /**
     * push
     * @param {Value} value
     * @returns {number}
     */
    push(value: Value): number;
    /**
     * pop
     * @returns {Value | undefined}
     */
    pop(): Value | undefined;
    /**
     * size
     * @returns {number}
     */
    get size(): number;
    /**
     * Iterator
     * @returns {IterableIterator<Value>}
     */
    [Symbol.iterator](): IterableIterator<Value>;
    #private;
}
