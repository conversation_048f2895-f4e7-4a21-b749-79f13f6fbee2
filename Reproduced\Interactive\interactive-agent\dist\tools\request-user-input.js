/**
 * Simplified request-user-input tool
 * Streamlined version of interactive-mcp tool with essential functionality only
 */
import { z } from 'zod';
import { ToolResultUtils } from './types.js';
import { getUserInput } from '../utils/input.js';
// Zod schema for request user input arguments
const RequestUserInputSchema = z.object({
    projectName: z.string().min(1, 'Project name is required'),
    message: z.string().min(1, 'Message is required'),
    predefinedOptions: z.array(z.string()).optional()
});
// Enhanced tool handler implementation with better type safety
const handleRequestUserInput = async (args) => {
    // Parse and validate the arguments using our schema
    const validatedArgs = RequestUserInputSchema.parse(args);
    const { projectName, message, predefinedOptions } = validatedArgs;
    try {
        const result = await getUserInput(projectName, message, predefinedOptions);
        return ToolResultUtils.success(`User response: ${result.response}`);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return ToolResultUtils.error(`Error getting user input: ${errorMessage}`);
    }
};
// Simplified capability info
export const requestUserInputCapability = {
    name: 'request_user_input',
    description: 'Request input from user with optional predefined choices'
};
// Enhanced tool definition with better type safety
export const requestUserInputTool = {
    name: 'request_user_input',
    description: 'Request input from the user during tool execution',
    inputSchema: RequestUserInputSchema,
    handler: handleRequestUserInput
};
//# sourceMappingURL=request-user-input.js.map