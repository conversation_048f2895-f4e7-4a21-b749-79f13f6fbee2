{
    "github.copilot.chat.summarizeAgentConversationHistory.enabled": false,
    "chat.agent.enabled": true,
    "chat.agent.maxRequests": 100,
    "chat.tools.autoApprove": true,
    "github.copilot.chat.agent.runTasks": true,
    "chat.mcp.discovery.enabled": true,
    "github.copilot.chat.agent.autoFix": true,
    "window.zoomLevel": -0.1,
    "editor.mouseWheelZoom": true,
    "editor.fontFamily": "'JetBrainsMono Nerd Font', <PERSON><PERSON><PERSON>, 'Courier New', monospace",
    "editor.fontSize": 12,
    "terminal.integrated.fontFamily": "'JetBrainsMono Nerd Font', Consolas, 'Courier New', monospace",
    "terminal.integrated.fontLigatures.enabled": true,
    "terminal.integrated.fontSize": 12,
    "terminal.integrated.fontWeight": "normal",
    "terminal.integrated.mouseWheelZoom": true,
    "editor.codeLensFontSize": 12,
    "editor.codeLensFontFamily": "'JetBrainsMono Nerd Font', <PERSON><PERSON><PERSON>, 'Courier New', monospace",
    "git.ignoreLimitWarning": true,
}