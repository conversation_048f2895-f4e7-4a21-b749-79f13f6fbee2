#!/usr/bin/env node

/**
 * Démonstration des capacités de détection de plateforme
 * et de spawn de terminal du module interactive-agent
 */

import * as platform from '../dist/platform/index.js';
import * as input from '../dist/utils/input.js';

async function demonstratePlatformDetection() {
  console.log('=== Démonstration de détection de plateforme ===\n');
  
  // Informations de base sur la plateforme
  const platformInfo = platform.getPlatformInfo();
  console.log('Informations de plateforme:');
  console.log(`  OS: ${platformInfo.platform}`);
  console.log(`  WSL: ${platformInfo.isWSL}`);
  console.log(`  MSYS: ${platformInfo.isMSYS}`);
  console.log(`  Shell: ${platformInfo.shell}`);
  console.log(`  Affichage graphique: ${platformInfo.hasDisplay}`);
  console.log(`  Environnement headless: ${platformInfo.isHeadless}`);
  console.log(`  Peut spawn terminal: ${platformInfo.canSpawnTerminal}`);
  console.log(`  Terminaux disponibles: ${platformInfo.availableTerminals.join(', ')}\n`);
  
  // Résumé de plateforme
  const summary = platform.getPlatformSummary();
  console.log('Résumé de plateforme:');
  console.log(`  Type: ${summary.type}`);
  console.log(`  Peut spawn terminal: ${summary.canSpawnTerminal}`);
  console.log(`  Shell préféré: ${summary.preferredShell}`);
  console.log(`  Nombre de terminaux: ${summary.terminalCount}\n`);
  
  // Capacités de terminal
  const capabilities = platform.getTerminalCapabilities();
  console.log('Capacités de terminal:');
  console.log(`  Peut spawn: ${capabilities.canSpawn}`);
  console.log(`  Terminal préféré: ${capabilities.preferredTerminal}`);
  console.log(`  Terminaux de fallback: ${capabilities.fallbackTerminals.join(', ')}\n`);
}

async function demonstrateTerminalFallbacks() {
  console.log('=== Démonstration des fallbacks de terminal ===\n');
  
  try {
    // Détecter les terminaux disponibles
    const availableTerminals = await platform.detectAvailableTerminals();
    console.log('Terminaux détectés:');
    availableTerminals.forEach(terminal => {
      console.log(`  ${terminal.name} (${terminal.command}) - Priorité: ${terminal.priority}`);
    });
    console.log();
    
    // Stratégie de fallback
    const fallbackStrategy = await platform.getFallbackStrategy();
    console.log('Stratégie de fallback:');
    console.log(`  Utiliser console: ${fallbackStrategy.useConsole}`);
    if (fallbackStrategy.preferredTerminal) {
      console.log(`  Terminal préféré: ${fallbackStrategy.preferredTerminal.name}`);
    }
    console.log(`  Terminaux de fallback: ${fallbackStrategy.fallbackTerminals.length}`);
    console.log();
    
    // Support des terminaux GUI
    const supportsGUI = platform.supportsGUITerminals();
    console.log(`Support des terminaux GUI: ${supportsGUI}\n`);
    
  } catch (error) {
    console.error('Erreur lors de la détection des fallbacks:', error.message);
  }
}

async function demonstrateSpawnCapabilities() {
  console.log('=== Démonstration des capacités de spawn ===\n');
  
  if (!platform.canSpawnTerminal()) {
    console.log('Le spawn de terminal n\'est pas disponible sur cette plateforme.\n');
    return;
  }
  
  console.log('Test de spawn de terminal...');
  
  try {
    // Test simple de spawn
    const result = platform.spawnInTerminal('echo', ['Hello from spawned terminal!'], {
      windowTitle: 'Test Interactive Agent'
    });
    
    if (result.success) {
      console.log('✅ Spawn de terminal réussi!');
      console.log(`   PID: ${result.process?.pid || 'N/A'}`);
    } else {
      console.log('❌ Échec du spawn de terminal');
      console.log(`   Erreur: ${result.error}`);
    }
    
  } catch (error) {
    console.log('❌ Erreur lors du test de spawn:', error.message);
  }
  
  console.log();
}

async function demonstrateInputCapabilities() {
  console.log('=== Démonstration des capacités d\'input ===\n');
  
  const inputPlatformInfo = input.getInputPlatformInfo();
  console.log('Informations d\'input:');
  console.log(`  Plateforme: ${inputPlatformInfo.platform}`);
  console.log(`  Peut spawn terminal: ${inputPlatformInfo.canSpawnTerminal}`);
  console.log(`  Terminal input disponible: ${input.isTerminalInputAvailable()}`);
  console.log();
  
  // Note: On ne fait pas d'input réel dans cette démo pour éviter de bloquer
  console.log('Note: Les fonctions d\'input sont disponibles mais non testées dans cette démo');
  console.log('      pour éviter de bloquer l\'exécution.\n');
}

async function main() {
  console.log('🚀 Démonstration du module de détection de plateforme Interactive Agent\n');
  
  try {
    await demonstratePlatformDetection();
    await demonstrateTerminalFallbacks();
    await demonstrateSpawnCapabilities();
    await demonstrateInputCapabilities();
    
    console.log('✅ Démonstration terminée avec succès!');
    
  } catch (error) {
    console.error('❌ Erreur lors de la démonstration:', error);
    process.exit(1);
  }
}

// Exécuter la démonstration
main().catch(console.error);

export {
  demonstratePlatformDetection,
  demonstrateTerminalFallbacks,
  demonstrateSpawnCapabilities,
  demonstrateInputCapabilities
};
