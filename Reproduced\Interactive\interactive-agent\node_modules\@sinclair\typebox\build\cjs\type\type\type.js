"use strict";

Object.defineProperty(exports, "__esModule", { value: true });
exports.Rest = exports.Required = exports.RegExp = exports.Ref = exports.Recursive = exports.Record = exports.ReadonlyOptional = exports.Readonly = exports.Promise = exports.Pick = exports.Partial = exports.Parameters = exports.Optional = exports.Omit = exports.Object = exports.Number = exports.Null = exports.Not = exports.Never = exports.Module = exports.Mapped = exports.Literal = exports.KeyOf = exports.Iterator = exports.Uppercase = exports.Lowercase = exports.Uncapitalize = exports.Capitalize = exports.Intersect = exports.Integer = exports.Instantiate = exports.InstanceType = exports.Index = exports.Function = exports.Extract = exports.Extends = exports.Exclude = exports.Enum = exports.Date = exports.ConstructorParameters = exports.Constructor = exports.Const = exports.Composite = exports.Boolean = exports.BigInt = exports.Awaited = exports.AsyncIterator = exports.Array = exports.Argument = exports.Any = void 0;
exports.Void = exports.Unsafe = exports.Unknown = exports.Union = exports.Undefined = exports.Uint8Array = exports.Tuple = exports.Transform = exports.TemplateLiteral = exports.Symbol = exports.String = exports.ReturnType = void 0;
// ------------------------------------------------------------------
// Type: Module
// ------------------------------------------------------------------
var index_1 = require("../any/index");
Object.defineProperty(exports, "Any", { enumerable: true, get: function () { return index_1.Any; } });
var index_2 = require("../argument/index");
Object.defineProperty(exports, "Argument", { enumerable: true, get: function () { return index_2.Argument; } });
var index_3 = require("../array/index");
Object.defineProperty(exports, "Array", { enumerable: true, get: function () { return index_3.Array; } });
var index_4 = require("../async-iterator/index");
Object.defineProperty(exports, "AsyncIterator", { enumerable: true, get: function () { return index_4.AsyncIterator; } });
var index_5 = require("../awaited/index");
Object.defineProperty(exports, "Awaited", { enumerable: true, get: function () { return index_5.Awaited; } });
var index_6 = require("../bigint/index");
Object.defineProperty(exports, "BigInt", { enumerable: true, get: function () { return index_6.BigInt; } });
var index_7 = require("../boolean/index");
Object.defineProperty(exports, "Boolean", { enumerable: true, get: function () { return index_7.Boolean; } });
var index_8 = require("../composite/index");
Object.defineProperty(exports, "Composite", { enumerable: true, get: function () { return index_8.Composite; } });
var index_9 = require("../const/index");
Object.defineProperty(exports, "Const", { enumerable: true, get: function () { return index_9.Const; } });
var index_10 = require("../constructor/index");
Object.defineProperty(exports, "Constructor", { enumerable: true, get: function () { return index_10.Constructor; } });
var index_11 = require("../constructor-parameters/index");
Object.defineProperty(exports, "ConstructorParameters", { enumerable: true, get: function () { return index_11.ConstructorParameters; } });
var index_12 = require("../date/index");
Object.defineProperty(exports, "Date", { enumerable: true, get: function () { return index_12.Date; } });
var index_13 = require("../enum/index");
Object.defineProperty(exports, "Enum", { enumerable: true, get: function () { return index_13.Enum; } });
var index_14 = require("../exclude/index");
Object.defineProperty(exports, "Exclude", { enumerable: true, get: function () { return index_14.Exclude; } });
var index_15 = require("../extends/index");
Object.defineProperty(exports, "Extends", { enumerable: true, get: function () { return index_15.Extends; } });
var index_16 = require("../extract/index");
Object.defineProperty(exports, "Extract", { enumerable: true, get: function () { return index_16.Extract; } });
var index_17 = require("../function/index");
Object.defineProperty(exports, "Function", { enumerable: true, get: function () { return index_17.Function; } });
var index_18 = require("../indexed/index");
Object.defineProperty(exports, "Index", { enumerable: true, get: function () { return index_18.Index; } });
var index_19 = require("../instance-type/index");
Object.defineProperty(exports, "InstanceType", { enumerable: true, get: function () { return index_19.InstanceType; } });
var index_20 = require("../instantiate/index");
Object.defineProperty(exports, "Instantiate", { enumerable: true, get: function () { return index_20.Instantiate; } });
var index_21 = require("../integer/index");
Object.defineProperty(exports, "Integer", { enumerable: true, get: function () { return index_21.Integer; } });
var index_22 = require("../intersect/index");
Object.defineProperty(exports, "Intersect", { enumerable: true, get: function () { return index_22.Intersect; } });
var index_23 = require("../intrinsic/index");
Object.defineProperty(exports, "Capitalize", { enumerable: true, get: function () { return index_23.Capitalize; } });
Object.defineProperty(exports, "Uncapitalize", { enumerable: true, get: function () { return index_23.Uncapitalize; } });
Object.defineProperty(exports, "Lowercase", { enumerable: true, get: function () { return index_23.Lowercase; } });
Object.defineProperty(exports, "Uppercase", { enumerable: true, get: function () { return index_23.Uppercase; } });
var index_24 = require("../iterator/index");
Object.defineProperty(exports, "Iterator", { enumerable: true, get: function () { return index_24.Iterator; } });
var index_25 = require("../keyof/index");
Object.defineProperty(exports, "KeyOf", { enumerable: true, get: function () { return index_25.KeyOf; } });
var index_26 = require("../literal/index");
Object.defineProperty(exports, "Literal", { enumerable: true, get: function () { return index_26.Literal; } });
var index_27 = require("../mapped/index");
Object.defineProperty(exports, "Mapped", { enumerable: true, get: function () { return index_27.Mapped; } });
var index_28 = require("../module/index");
Object.defineProperty(exports, "Module", { enumerable: true, get: function () { return index_28.Module; } });
var index_29 = require("../never/index");
Object.defineProperty(exports, "Never", { enumerable: true, get: function () { return index_29.Never; } });
var index_30 = require("../not/index");
Object.defineProperty(exports, "Not", { enumerable: true, get: function () { return index_30.Not; } });
var index_31 = require("../null/index");
Object.defineProperty(exports, "Null", { enumerable: true, get: function () { return index_31.Null; } });
var index_32 = require("../number/index");
Object.defineProperty(exports, "Number", { enumerable: true, get: function () { return index_32.Number; } });
var index_33 = require("../object/index");
Object.defineProperty(exports, "Object", { enumerable: true, get: function () { return index_33.Object; } });
var index_34 = require("../omit/index");
Object.defineProperty(exports, "Omit", { enumerable: true, get: function () { return index_34.Omit; } });
var index_35 = require("../optional/index");
Object.defineProperty(exports, "Optional", { enumerable: true, get: function () { return index_35.Optional; } });
var index_36 = require("../parameters/index");
Object.defineProperty(exports, "Parameters", { enumerable: true, get: function () { return index_36.Parameters; } });
var index_37 = require("../partial/index");
Object.defineProperty(exports, "Partial", { enumerable: true, get: function () { return index_37.Partial; } });
var index_38 = require("../pick/index");
Object.defineProperty(exports, "Pick", { enumerable: true, get: function () { return index_38.Pick; } });
var index_39 = require("../promise/index");
Object.defineProperty(exports, "Promise", { enumerable: true, get: function () { return index_39.Promise; } });
var index_40 = require("../readonly/index");
Object.defineProperty(exports, "Readonly", { enumerable: true, get: function () { return index_40.Readonly; } });
var index_41 = require("../readonly-optional/index");
Object.defineProperty(exports, "ReadonlyOptional", { enumerable: true, get: function () { return index_41.ReadonlyOptional; } });
var index_42 = require("../record/index");
Object.defineProperty(exports, "Record", { enumerable: true, get: function () { return index_42.Record; } });
var index_43 = require("../recursive/index");
Object.defineProperty(exports, "Recursive", { enumerable: true, get: function () { return index_43.Recursive; } });
var index_44 = require("../ref/index");
Object.defineProperty(exports, "Ref", { enumerable: true, get: function () { return index_44.Ref; } });
var index_45 = require("../regexp/index");
Object.defineProperty(exports, "RegExp", { enumerable: true, get: function () { return index_45.RegExp; } });
var index_46 = require("../required/index");
Object.defineProperty(exports, "Required", { enumerable: true, get: function () { return index_46.Required; } });
var index_47 = require("../rest/index");
Object.defineProperty(exports, "Rest", { enumerable: true, get: function () { return index_47.Rest; } });
var index_48 = require("../return-type/index");
Object.defineProperty(exports, "ReturnType", { enumerable: true, get: function () { return index_48.ReturnType; } });
var index_49 = require("../string/index");
Object.defineProperty(exports, "String", { enumerable: true, get: function () { return index_49.String; } });
var index_50 = require("../symbol/index");
Object.defineProperty(exports, "Symbol", { enumerable: true, get: function () { return index_50.Symbol; } });
var index_51 = require("../template-literal/index");
Object.defineProperty(exports, "TemplateLiteral", { enumerable: true, get: function () { return index_51.TemplateLiteral; } });
var index_52 = require("../transform/index");
Object.defineProperty(exports, "Transform", { enumerable: true, get: function () { return index_52.Transform; } });
var index_53 = require("../tuple/index");
Object.defineProperty(exports, "Tuple", { enumerable: true, get: function () { return index_53.Tuple; } });
var index_54 = require("../uint8array/index");
Object.defineProperty(exports, "Uint8Array", { enumerable: true, get: function () { return index_54.Uint8Array; } });
var index_55 = require("../undefined/index");
Object.defineProperty(exports, "Undefined", { enumerable: true, get: function () { return index_55.Undefined; } });
var index_56 = require("../union/index");
Object.defineProperty(exports, "Union", { enumerable: true, get: function () { return index_56.Union; } });
var index_57 = require("../unknown/index");
Object.defineProperty(exports, "Unknown", { enumerable: true, get: function () { return index_57.Unknown; } });
var index_58 = require("../unsafe/index");
Object.defineProperty(exports, "Unsafe", { enumerable: true, get: function () { return index_58.Unsafe; } });
var index_59 = require("../void/index");
Object.defineProperty(exports, "Void", { enumerable: true, get: function () { return index_59.Void; } });
