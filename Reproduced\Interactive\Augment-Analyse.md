# Interactive-MCP : Analyse Architecturale et Technique

## Vue d'ensemble

Interactive-MCP est un serveur MCP (Model Context Protocol) développé en Node.js/TypeScript qui facilite la communication interactive entre les LLMs et les utilisateurs. Il permet aux assistants IA de poser des questions directement aux utilisateurs et de recevoir des réponses en temps réel via des interfaces graphiques natives du système d'exploitation.

## Architecture Générale

```
┌─────────────────────────────────────────────────────────────────┐
│                    CLIENT MCP (Claude Desktop, VS Code)         │
└─────────────────────┬───────────────────────────────────────────┘
                      │ MCP Protocol (JSON-RPC via stdio)
┌─────────────────────▼───────────────────────────────────────────┐
│                 INTERACTIVE-MCP SERVER                          │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │                   index.ts (Point d'entrée)                 ││
│  │  - Initialisation serveur MCP                               ││
│  │  - Gestion des arguments CLI                                ││
│  │  - Enregistrement des outils                               ││
│  └─────────────────────────────────────────────────────────────┘│
│                                │                                │
│  ┌─────────────────────────────▼────────────────────────────────┐│
│  │              TOOL DEFINITIONS                                ││
│  │  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐││
│  │  │request_user_    │ │message_complete_│ │intensive_chat   │││
│  │  │input            │ │notification     │ │(start/ask/stop) │││
│  │  └─────────────────┘ └─────────────────┘ └─────────────────┘││
│  └─────────────────────────────────────────────────────────────┘│
│                                │                                │
│  ┌─────────────────────────────▼────────────────────────────────┐│
│  │                    COMMANDS                                  ││
│  │  ┌─────────────────┐           ┌─────────────────────────────┐││
│  │  │     INPUT       │           │      INTENSIVE-CHAT         │││
│  │  │  - getCmdWindow │           │  - startIntensiveChatSession│││
│  │  │    Input()      │           │  - askQuestionInSession     │││
│  │  │  - UI spawning  │           │  - stopIntensiveChatSession │││
│  │  └─────────────────┘           └─────────────────────────────┘││
│  └─────────────────────────────────────────────────────────────┘│
│                                │                                │
│  ┌─────────────────────────────▼────────────────────────────────┐│
│  │                 UI COMPONENTS                                ││
│  │  ┌─────────────────┐           ┌─────────────────────────────┐││
│  │  │InteractiveInput │           │    React/Ink Components     │││
│  │  │  - Gestion      │           │  - Terminal UI rendering    │││
│  │  │    clavier      │           │  - Progress bars            │││
│  │  │  - Options      │           │  - Countdown timers         │││
│  │  │    prédéfinies  │           │  - Chat history display     │││
│  │  └─────────────────┘           └─────────────────────────────┘││
│  └─────────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                SYSTÈME D'EXPLOITATION                           │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────────┐│
│  │   Notifications │ │  Terminal/CMD   │ │   Fichiers temp     ││
│  │   (node-notifier│ │   Windows       │ │   (Communication    ││
│  │    OS natives)  │ │   (spawn)       │ │    inter-processus) ││
│  └─────────────────┘ └─────────────────┘ └─────────────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

## Outils MCP Exposés

### 1. request_user_input
**Fonction** : Demande une saisie utilisateur ponctuelle
**Paramètres** :
- `projectName` : Contexte/projet de la demande
- `message` : Question spécifique pour l'utilisateur  
- `predefinedOptions` : Options prédéfinies (optionnel)

### 2. message_complete_notification
**Fonction** : Envoie une notification système
**Paramètres** :
- `projectName` : Titre de la notification
- `message` : Corps de la notification

### 3. start_intensive_chat
**Fonction** : Démarre une session de chat persistante
**Paramètres** :
- `sessionTitle` : Titre de la session

### 4. ask_intensive_chat  
**Fonction** : Pose une question dans une session active
**Paramètres** :
- `sessionId` : ID de la session
- `question` : Texte de la question
- `predefinedOptions` : Options prédéfinies (optionnel)

### 5. stop_intensive_chat
**Fonction** : Ferme une session de chat active
**Paramètres** :
- `sessionId` : ID de la session à fermer

## Architecture des Données

### Types Principaux

```typescript
// Définition d'un outil MCP
interface ToolDefinition {
  capability: ToolCapabilityInfo;
  description: ToolRegistrationDescription;
  schema: ZodRawShape;
}

// Information de capacité d'un outil
interface ToolCapabilityInfo {
  description: string;
  parameters: object;
}

// Description d'enregistrement d'outil
type ToolRegistrationDescription = 
  | string 
  | ((timeout: number) => string);

// Information de session intensive
interface SessionInfo {
  id: string;
  process: ChildProcess;
  outputDir: string;
  lastHeartbeatTime: number;
  isActive: boolean;
  title: string;
  timeoutSeconds?: number;
}

// Message de chat
interface ChatMessage {
  text: string;
  isQuestion: boolean;
  answer?: string;
}
```

## Mécanismes de Communication

### 1. Communication MCP
- **Protocole** : JSON-RPC sur stdio
- **Transport** : StdioServerTransport
- **Validation** : Schémas Zod pour la validation des paramètres

### 2. Communication Inter-Processus
- **Fichiers temporaires** : Communication via système de fichiers
- **Heartbeat** : Fichiers de battement de cœur pour détecter les processus actifs
- **Polling** : Vérification périodique des nouveaux inputs/réponses

### 3. Gestion des Sessions
```
Session Lifecycle:
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   CREATE    │───▶│   ACTIVE    │───▶│  QUESTION   │───▶│   CLEANUP   │
│  - Spawn UI │    │- Heartbeat  │    │ - Wait resp │    │- Kill proc  │
│  - Setup    │    │- Monitor    │    │ - Timeout   │    │- Remove     │
│    dirs     │    │  files      │    │   handling  │    │   files     │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

## Gestion Multi-Plateforme

### Spawn de Processus par OS
```typescript
// macOS - Terminal.app
const command = `osascript -e 'tell application "Terminal" to activate' 
                 -e 'tell application "Terminal" to do script "${nodeCommand}"'`;

// Windows - CMD
spawn('node', [uiScriptPath, sessionId], {
  windowsHide: false,
  detached: true
});

// Linux - Terminal générique  
spawn('node', [uiScriptPath, sessionId], {
  detached: true
});
```

## Interface Utilisateur (React/Ink)

### Composants Principaux

#### InteractiveInput
- **Navigation** : Flèches haut/bas pour options prédéfinies
- **Saisie libre** : Basculement automatique vers mode texte
- **Validation** : Soumission par Entrée
- **États** : Mode option vs mode saisie

#### Gestion des Timeouts
- **Countdown visuel** : Barre de progression
- **Auto-timeout** : Soumission automatique de `__TIMEOUT__`
- **Heartbeat** : Mise à jour périodique pour maintenir la session

## Diagramme de Séquence - Demande Simple

```mermaid
sequenceDiagram
    participant LLM as LLM/Client MCP
    participant Server as Interactive-MCP Server
    participant UI as Interface Utilisateur
    participant OS as Système d'Exploitation

    LLM->>Server: request_user_input(projectName, message, options?)
    Server->>OS: Créer fichiers temporaires
    Server->>OS: Spawn processus UI détaché
    OS->>UI: Lancer interface React/Ink
    UI->>OS: Écrire heartbeat périodique
    UI->>UI: Afficher question + options
    UI->>UI: Attendre saisie utilisateur
    Note over UI: Utilisateur saisit réponse
    UI->>OS: Écrire réponse dans fichier temp
    UI->>UI: Fermer interface
    Server->>OS: Lire fichier de réponse (polling)
    Server->>OS: Nettoyer fichiers temporaires
    Server->>LLM: Retourner réponse utilisateur
```

## Diagramme de Séquence - Session Intensive

```mermaid
sequenceDiagram
    participant LLM as LLM/Client MCP
    participant Server as Interactive-MCP Server
    participant Session as Session Manager
    participant UI as Interface Chat
    participant OS as Système d'Exploitation

    LLM->>Server: start_intensive_chat(sessionTitle)
    Server->>Session: startIntensiveChatSession()
    Session->>OS: Créer répertoire session
    Session->>OS: Spawn processus UI persistant
    OS->>UI: Lancer interface chat React/Ink
    UI->>OS: Démarrer heartbeat continu
    Session->>LLM: Retourner sessionId

    loop Questions multiples
        LLM->>Server: ask_intensive_chat(sessionId, question, options?)
        Server->>Session: askQuestionInSession()
        Session->>OS: Écrire question dans fichier JSON
        UI->>OS: Détecter nouveau fichier (polling)
        UI->>UI: Afficher question dans historique
        UI->>UI: Attendre réponse utilisateur
        Note over UI: Utilisateur répond
        UI->>OS: Écrire réponse dans fichier
        Session->>OS: Lire réponse (polling)
        Session->>LLM: Retourner réponse
    end

    LLM->>Server: stop_intensive_chat(sessionId)
    Server->>Session: stopIntensiveChatSession()
    Session->>OS: Écrire signal de fermeture
    UI->>OS: Détecter signal et fermer
    Session->>OS: Nettoyer processus et fichiers
    Session->>LLM: Confirmer fermeture
```

## Gestion des Erreurs et Timeouts

### Stratégies de Timeout
| Composant | Timeout | Action |
|-----------|---------|--------|
| Interface utilisateur | 60s (configurable) | Retourne `__TIMEOUT__` |
| Heartbeat check | 2s | Marque session inactive |
| Polling réponse | 30s | Retourne message d'erreur |
| Cleanup processus | 500ms | Force kill si nécessaire |

### Mécanismes de Récupération
- **Heartbeat monitoring** : Détection automatique des processus morts
- **File cleanup** : Nettoyage automatique des fichiers temporaires
- **Process cleanup** : Terminaison forcée des processus orphelins
- **Session recovery** : Gestion des sessions interrompues

## Structure des Fichiers

```
src/
├── index.ts                    # Point d'entrée principal
├── constants.ts                # Constantes globales
├── commands/
│   ├── input/
│   │   ├── index.ts           # Logique input simple
│   │   └── ui.tsx             # Interface React/Ink
│   └── intensive-chat/
│       ├── index.ts           # Gestion sessions chat
│       └── ui.tsx             # Interface chat persistante
├── components/
│   └── InteractiveInput.tsx   # Composant input partagé
├── tool-definitions/
│   ├── types.ts               # Types TypeScript
│   ├── request-user-input.ts  # Définition outil input
│   ├── message-complete-notification.ts # Notifications
│   └── intensive-chat.ts      # Outils chat intensif
├── ui/
│   └── interactive-input.tsx  # Interface alternative
└── utils/
    └── logger.ts              # Système de logging
```

## Configuration et Déploiement

### Arguments CLI Supportés
```bash
interactive-mcp [options]
  -t, --timeout <seconds>     # Timeout par défaut (défaut: 60s)
  -d, --disable-tools <list>  # Outils à désactiver
  -h, --help                  # Aide
```

### Configuration Client MCP
```json
{
  "mcpServers": {
    "interactive": {
      "command": "npx",
      "args": ["-y", "interactive-mcp", "-t", "30"]
    }
  }
}
```

### Dépendances Clés
| Package | Version | Usage |
|---------|---------|-------|
| @modelcontextprotocol/sdk | ^1.10.2 | SDK MCP officiel |
| ink | ^5.2.0 | Interface terminal React |
| @inkjs/ui | ^2.0.0 | Composants UI Ink |
| node-notifier | ^10.0.1 | Notifications OS |
| yargs | ^17.7.2 | Parsing arguments CLI |
| zod | ^3.24.3 | Validation schémas |
| pino | ^9.6.0 | Logging structuré |

## Sécurité et Limitations

### Considérations de Sécurité
- **Processus détachés** : Risque de processus orphelins
- **Fichiers temporaires** : Nettoyage automatique requis
- **Accès système** : Nécessite permissions notifications/terminal

### Limitations Actuelles
- **Mono-utilisateur** : Une session par utilisateur système
- **Plateforme** : Optimisé pour macOS/Windows/Linux desktop
- **Réseau** : Pas de support distant (local uniquement)
- **Concurrence** : Gestion limitée des sessions multiples

## Métriques et Monitoring

### Logging
- **Niveau développement** : Logs détaillés vers fichier + console
- **Niveau production** : Logs silencieux
- **Fichiers logs** : `${tmpdir}/interactive-mcp-logs/dev.log`

### Monitoring Sessions
- **Heartbeat** : Vérification toutes les 2 secondes
- **Cleanup automatique** : Sessions inactives supprimées
- **Métriques** : Temps de réponse, taux de timeout, sessions actives

Cette analyse constitue la documentation technique complète du projet interactive-mcp, couvrant tous les aspects architecturaux, techniques et opérationnels nécessaires à sa compréhension et maintenance.
