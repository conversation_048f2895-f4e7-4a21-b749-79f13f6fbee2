#!/usr/bin/env node

/**
 * Test simple du module de détection de plateforme
 */

import * as platform from './dist/platform/index.js';
import * as input from './dist/utils/input.js';

console.log('🚀 Test du module de détection de plateforme Interactive Agent\n');

// Test des informations de plateforme
console.log('=== Informations de plateforme ===');
const platformInfo = platform.getPlatformInfo();
console.log('Platform:', platformInfo.platform);
console.log('WSL:', platformInfo.isWSL);
console.log('Shell:', platformInfo.shell);
console.log('Can spawn terminal:', platformInfo.canSpawnTerminal);
console.log('Available terminals:', platformInfo.availableTerminals.join(', '));
console.log('Has display:', platformInfo.hasDisplay);
console.log('Is headless:', platformInfo.isHeadless);
console.log();

// Test des capacités de terminal
console.log('=== Capacités de terminal ===');
const capabilities = platform.getTerminalCapabilities();
console.log('Can spawn:', capabilities.canSpawn);
console.log('Preferred terminal:', capabilities.preferredTerminal);
console.log('Fallback terminals:', capabilities.fallbackTerminals.join(', '));
console.log();

// Test des utilitaires de plateforme
console.log('=== Utilitaires de plateforme ===');
console.log('Platform type:', platform.getPlatformType());
console.log('Is Windows:', platform.isWindows());
console.log('Is macOS:', platform.isMacOS());
console.log('Is Linux:', platform.isLinux());
console.log('Is WSL:', platform.isWSL());
console.log();

// Test du résumé de plateforme
console.log('=== Résumé de plateforme ===');
const summary = platform.getPlatformSummary();
console.log('Type:', summary.type);
console.log('Can spawn terminal:', summary.canSpawnTerminal);
console.log('Has display:', summary.hasDisplay);
console.log('Is headless:', summary.isHeadless);
console.log('Preferred shell:', summary.preferredShell);
console.log('Terminal count:', summary.terminalCount);
console.log();

// Test des capacités d'input
console.log('=== Capacités d\'input ===');
console.log('Terminal input available:', input.isTerminalInputAvailable());
const inputPlatformInfo = input.getInputPlatformInfo();
console.log('Input platform:', inputPlatformInfo.platform);
console.log('Input can spawn terminal:', inputPlatformInfo.canSpawnTerminal);
console.log();

// Test de spawn de terminal (sans exécution réelle)
console.log('=== Test de spawn de terminal ===');
if (platform.canSpawnTerminal()) {
  console.log('✅ Le spawn de terminal est disponible');
  
  // Test de spawn simple (juste pour vérifier la configuration)
  const result = platform.spawnInTerminal('echo', ['Test message'], {
    windowTitle: 'Test Interactive Agent'
  });
  
  if (result.success) {
    console.log('✅ Configuration de spawn réussie');
    console.log('   PID:', result.process?.pid || 'N/A');
    
    // Fermer le processus immédiatement pour éviter de laisser des terminaux ouverts
    if (result.process) {
      result.process.kill();
    }
  } else {
    console.log('❌ Échec de la configuration de spawn');
    console.log('   Erreur:', result.error);
  }
} else {
  console.log('❌ Le spawn de terminal n\'est pas disponible');
}
console.log();

console.log('✅ Test terminé avec succès!');
