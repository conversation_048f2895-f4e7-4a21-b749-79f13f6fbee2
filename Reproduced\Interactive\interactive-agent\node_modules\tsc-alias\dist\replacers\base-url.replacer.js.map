{"version": 3, "file": "base-url.replacer.js", "sourceRoot": "", "sources": ["../../src/replacers/base-url.replacer.ts"], "names": [], "mappings": ";;AAQA,gDAAiD;AACjD,+BAAyC;AAEzC,oCAA0C;AAE1C,SAAwB,oBAAoB,CAAC,EAC3C,IAAI,EACJ,IAAI,EACJ,MAAM,EACiB;;IACvB,MAAM,cAAc,GAAG,MAAA,MAAA,IAAI,CAAC,KAAK,CAAC,IAAA,sBAAc,GAAE,CAAC,0CAAE,MAAM,0CAAE,IAAI,CAAC;IAClE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,cAAc,CAAC,CAAC;IAC5E,MAAM,CAAC,MAAM,CAAC,MAAM,CAClB,OAAO,cAAc,IAAI,QAAQ,EACjC,uCAAuC,IAAI,EAAE,CAC9C,CAAC;IAGF,IAAI,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;QAClC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC;KACb;IAGD,IACE,MAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,GAAG,MAAM,CAAC,OAAO,IAAI,cAAc,EAAE,CAAC,EAC3E;QACA,IAAI,YAAY,GAAW,aAAa,CACtC,IAAA,eAAQ,EACN,IAAA,cAAO,EAAC,IAAI,CAAC,EACb,MAAM,CAAC,SAAS;aACb,oBAAoB,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;aACxC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CACtB,CACF,CAAC;QACF,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACjC,YAAY,GAAG,IAAI,GAAG,YAAY,CAAC;SACpC;QACD,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,YAAY,CAAC,CAAC;QAExE,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAC3C,MAAM,eAAe,GACnB,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,YAAY,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACxE,MAAM,CAAC,MAAM,CAAC,KAAK,CACjB,uCAAuC,EACvC,eAAe,CAChB,CAAC;QAEF,MAAM,UAAU,GAAG,eAAe,CAAC,KAAK,CAAC,IAAA,sBAAc,GAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;QACvE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,UAAU,CAAC,CAAC;QACpE,OAAO,eAAe,CAAC,OAAO,CAAC,UAAU,EAAE,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC;KACvE;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAhDD,uCAgDC", "sourcesContent": ["/**\n * @file\n *\n * The baseUrl replacer replaces the import statement\n * with the baseUrl + import statement location.\n */\n\n/** */\nimport normalizePath = require('normalize-path');\nimport { dirname, relative } from 'path';\nimport { AliasReplacerArguments } from '../interfaces';\nimport { newStringRegex } from '../utils';\n\nexport default function replaceBaseUrlImport({\n  orig,\n  file,\n  config\n}: AliasReplacerArguments): string {\n  const requiredModule = orig.match(newStringRegex())?.groups?.path;\n  config.output.debug('base-url replacer - requiredModule: ', requiredModule);\n  config.output.assert(\n    typeof requiredModule == 'string',\n    `Unexpected import statement pattern ${orig}`\n  );\n\n  // Check if import is already resolved.\n  if (requiredModule.startsWith('.')) {\n    config.output.debug('base-url replacer - already resolved');\n    return orig;\n  }\n\n  // If there are files matching the target, resolve the path.\n  if (\n    config.pathCache.existsResolvedAlias(`${config.outPath}/${requiredModule}`)\n  ) {\n    let relativePath: string = normalizePath(\n      relative(\n        dirname(file),\n        config.pathCache\n          .getAbsoluteAliasPath(config.outPath, '')\n          .replace('---', '')\n      )\n    );\n    if (!relativePath.startsWith('.')) {\n      relativePath = './' + relativePath;\n    }\n    config.output.debug('base-url replacer - relativePath: ', relativePath);\n\n    const index = orig.indexOf(requiredModule);\n    const newImportScript =\n      orig.substring(0, index) + relativePath + '/' + orig.substring(index);\n    config.output.debug(\n      'base-url replacer - newImportScript: ',\n      newImportScript\n    );\n\n    const modulePath = newImportScript.match(newStringRegex()).groups.path;\n    config.output.debug('base-url replacer - modulePath: ', modulePath);\n    return newImportScript.replace(modulePath, normalizePath(modulePath));\n  }\n  return orig;\n}\n"]}