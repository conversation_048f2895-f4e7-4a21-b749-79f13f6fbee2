{"version": 3, "sources": ["../src/set-array.ts", "../src/gen-mapping.ts", "../src/sourcemap-segment.ts"], "mappings": ";AAUO,IAAM,WAAN,MAAoC;AAAA,EAIzC,cAAc;AACZ,SAAK,WAAW,EAAE,WAAW,KAAK;AAClC,SAAK,QAAQ,CAAC;AAAA,EAChB;AACF;AAWA,SAAS,KAAoB,KAAgC;AAC3D,SAAO;AACT;AAKO,SAAS,IAAmB,QAAqB,KAA4B;AAClF,SAAO,KAAK,MAAM,EAAE,SAAS,GAAG;AAClC;AAMO,SAAS,IAAmB,QAAqB,KAAgB;AAEtE,QAAM,QAAQ,IAAI,QAAQ,GAAG;AAC7B,MAAI,UAAU,OAAW,QAAO;AAEhC,QAAM,EAAE,OAAO,UAAU,QAAQ,IAAI,KAAK,MAAM;AAEhD,QAAM,SAAS,MAAM,KAAK,GAAG;AAC7B,SAAQ,QAAQ,GAAG,IAAI,SAAS;AAClC;AAgBO,SAAS,OAAsB,QAAqB,KAAc;AACvE,QAAM,QAAQ,IAAI,QAAQ,GAAG;AAC7B,MAAI,UAAU,OAAW;AAEzB,QAAM,EAAE,OAAO,UAAU,QAAQ,IAAI,KAAK,MAAM;AAChD,WAAS,IAAI,QAAQ,GAAG,IAAI,MAAM,QAAQ,KAAK;AAC7C,UAAM,IAAI,MAAM,CAAC;AACjB,UAAM,IAAI,CAAC,IAAI;AACf,YAAQ,CAAC;AAAA,EACX;AACA,UAAQ,GAAG,IAAI;AACf,QAAM,IAAI;AACZ;;;AChFA;AAAA,EACE;AAAA,OAGK;AACP,SAAS,UAAU,uBAAuB;;;ACKnC,IAAM,SAAS;AACf,IAAM,gBAAgB;AACtB,IAAM,cAAc;AACpB,IAAM,gBAAgB;AACtB,IAAM,cAAc;;;ADsB3B,IAAM,UAAU;AAKT,IAAM,aAAN,MAAiB;AAAA,EAWtB,YAAY,EAAE,MAAM,WAAW,IAAa,CAAC,GAAG;AAC9C,SAAK,SAAS,IAAI,SAAS;AAC3B,SAAK,WAAW,IAAI,SAAS;AAC7B,SAAK,kBAAkB,CAAC;AACxB,SAAK,YAAY,CAAC;AAGlB,SAAK,OAAO;AACZ,SAAK,aAAa;AAClB,SAAK,cAAc,IAAI,SAAS;AAAA,EAClC;AACF;AAgBA,SAASA,MAAK,KAAyB;AACrC,SAAO;AACT;AAoCO,SAAS,WACd,KACA,SACA,WACA,QACA,YACA,cACA,MACA,SACM;AACN,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAoCO,SAAS,WACd,KACA,SAOM;AACN,SAAO,mBAAmB,OAAO,KAAK,OAAmD;AAC3F;AAOO,IAAM,kBAAqC,CAChD,KACA,SACA,WACA,QACA,YACA,cACA,MACA,YACG;AACH,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAOO,IAAM,kBAAqC,CAAC,KAAK,YAAY;AAClE,SAAO,mBAAmB,MAAM,KAAK,OAAmD;AAC1F;AAKO,SAAS,iBAAiB,KAAiB,QAAgB,SAA8B;AAC9F,QAAM;AAAA,IACJ,UAAU;AAAA,IACV,iBAAiB;AAAA;AAAA,EAEnB,IAAIA,MAAK,GAAG;AACZ,QAAM,QAAQ,IAAI,SAAS,MAAM;AACjC,iBAAe,KAAK,IAAI;AAE1B;AAEO,SAAS,UAAU,KAAiB,QAAgB,SAAS,MAAM;AACxE,QAAM;AAAA,IACJ,UAAU;AAAA,IACV,iBAAiB;AAAA,IACjB,aAAa;AAAA;AAAA,EAEf,IAAIA,MAAK,GAAG;AACZ,QAAM,QAAQ,IAAI,SAAS,MAAM;AACjC,MAAI,UAAU,eAAe,OAAQ,gBAAe,KAAK,IAAI;AAE7D,MAAI,OAAQ,KAAI,YAAY,KAAK;AAAA,MAC5B,QAAO,YAAY,KAAK;AAC/B;AAMO,SAAS,aAAa,KAAmC;AAC9D,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,UAAU;AAAA,IACV,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,aAAa;AAAA;AAAA;AAAA,EAGf,IAAIA,MAAK,GAAG;AACZ,wBAAsB,QAAQ;AAE9B,SAAO;AAAA,IACL,SAAS;AAAA,IACT,MAAM,IAAI,QAAQ;AAAA,IAClB,OAAO,MAAM;AAAA,IACb,YAAY,IAAI,cAAc;AAAA,IAC9B,SAAS,QAAQ;AAAA,IACjB;AAAA,IACA;AAAA;AAAA;AAAA,IAGA,YAAY,WAAW;AAAA,EACzB;AACF;AAMO,SAAS,aAAa,KAAmC;AAC9D,QAAM,UAAU,aAAa,GAAG;AAChC,SAAO,OAAO,OAAO,CAAC,GAAG,SAAS;AAAA;AAAA;AAAA,IAGhC,UAAU,OAAO,QAAQ,QAAgC;AAAA,EAC3D,CAAC;AACH;AAKO,SAAS,QAAQ,OAAmC;AACzD,QAAM,MAAM,IAAI,SAAS,KAAK;AAC9B,QAAM,MAAM,IAAI,WAAW,EAAE,MAAM,IAAI,MAAM,YAAY,IAAI,WAAW,CAAC;AAEzE,SAAOA,MAAK,GAAG,EAAE,QAAQ,IAAI,KAAK;AAClC,SAAOA,MAAK,GAAG,EAAE,UAAU,IAAI,OAAmB;AAClD,EAAAA,MAAK,GAAG,EAAE,kBAAkB,IAAI,kBAAkB,IAAI,QAAQ,IAAI,MAAM,IAAI;AAC5E,EAAAA,MAAK,GAAG,EAAE,YAAY,gBAAgB,GAAG;AAEzC,MAAI,IAAI,WAAY,QAAOA,MAAK,GAAG,EAAE,aAAa,IAAI,UAAU;AAEhE,SAAO;AACT;AAMO,SAAS,YAAY,KAA4B;AACtD,QAAM,MAAiB,CAAC;AACxB,QAAM,EAAE,WAAW,UAAU,UAAU,SAAS,QAAQ,MAAM,IAAIA,MAAK,GAAG;AAE1E,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,UAAM,OAAO,SAAS,CAAC;AACvB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAM,MAAM,KAAK,CAAC;AAElB,YAAM,YAAY,EAAE,MAAM,IAAI,GAAG,QAAQ,IAAI,MAAM,EAAE;AACrD,UAAI,SAA6B;AACjC,UAAI,WAA4B;AAChC,UAAI,OAA2B;AAE/B,UAAI,IAAI,WAAW,GAAG;AACpB,iBAAS,QAAQ,MAAM,IAAI,aAAa,CAAC;AACzC,mBAAW,EAAE,MAAM,IAAI,WAAW,IAAI,GAAG,QAAQ,IAAI,aAAa,EAAE;AAEpE,YAAI,IAAI,WAAW,EAAG,QAAO,MAAM,MAAM,IAAI,WAAW,CAAC;AAAA,MAC3D;AAEA,UAAI,KAAK,EAAE,WAAW,QAAQ,UAAU,KAAK,CAAY;AAAA,IAC3D;AAAA,EACF;AAEA,SAAO;AACT;AAGA,SAAS,mBACP,UACA,KACA,SACA,WACA,QACA,YACA,cACA,MACA,SACM;AACN,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,UAAU;AAAA,IACV,iBAAiB;AAAA,IACjB,QAAQ;AAAA;AAAA,EAEV,IAAIA,MAAK,GAAG;AACZ,QAAM,OAAO,SAAS,UAAU,OAAO;AACvC,QAAM,QAAQ,eAAe,MAAM,SAAS;AAE5C,MAAI,CAAC,QAAQ;AACX,QAAI,YAAY,eAAe,MAAM,KAAK,EAAG;AAC7C,WAAO,OAAO,MAAM,OAAO,CAAC,SAAS,CAAC;AAAA,EACxC;AAIA,SAAe,UAAU;AACzB,SAAe,YAAY;AAE3B,QAAM,eAAe,IAAI,SAAS,MAAM;AACxC,QAAM,aAAa,OAAO,IAAI,OAAO,IAAI,IAAI;AAC7C,MAAI,iBAAiB,eAAe,OAAQ,gBAAe,YAAY,IAAI,4BAAW;AAGtF,MAAI,YAAY,WAAW,MAAM,OAAO,cAAc,YAAY,cAAc,UAAU,GAAG;AAC3F;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,OACI,CAAC,WAAW,cAAc,YAAY,cAAc,UAAU,IAC9D,CAAC,WAAW,cAAc,YAAY,YAAY;AAAA,EACxD;AACF;AAEA,SAAS,OAAU,MAAkC;AAErD;AAEA,SAAS,SAAY,KAAY,OAAoB;AACnD,WAAS,IAAI,IAAI,QAAQ,KAAK,OAAO,KAAK;AACxC,QAAI,CAAC,IAAI,CAAC;AAAA,EACZ;AACA,SAAO,IAAI,KAAK;AAClB;AAEA,SAAS,eAAe,MAA0B,WAA2B;AAC3E,MAAI,QAAQ,KAAK;AACjB,WAAS,IAAI,QAAQ,GAAG,KAAK,GAAG,QAAQ,KAAK;AAC3C,UAAM,UAAU,KAAK,CAAC;AACtB,QAAI,aAAa,QAAQ,MAAM,EAAG;AAAA,EACpC;AACA,SAAO;AACT;AAEA,SAAS,OAAU,OAAY,OAAe,OAAU;AACtD,WAAS,IAAI,MAAM,QAAQ,IAAI,OAAO,KAAK;AACzC,UAAM,CAAC,IAAI,MAAM,IAAI,CAAC;AAAA,EACxB;AACA,QAAM,KAAK,IAAI;AACjB;AAEA,SAAS,sBAAsB,UAAgC;AAC7D,QAAM,EAAE,OAAO,IAAI;AACnB,MAAI,MAAM;AACV,WAAS,IAAI,MAAM,GAAG,KAAK,GAAG,MAAM,GAAG,KAAK;AAC1C,QAAI,SAAS,CAAC,EAAE,SAAS,EAAG;AAAA,EAC9B;AACA,MAAI,MAAM,OAAQ,UAAS,SAAS;AACtC;AAEA,SAAS,OAAkC,QAAqB,OAAY;AAC1E,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAK,KAAI,QAAQ,MAAM,CAAC,CAAC;AAC7D;AAEA,SAAS,eAAe,MAA0B,OAAwB;AAGxE,MAAI,UAAU,EAAG,QAAO;AAExB,QAAM,OAAO,KAAK,QAAQ,CAAC;AAI3B,SAAO,KAAK,WAAW;AACzB;AAEA,SAAS,WACP,MACA,OACA,cACA,YACA,cACA,YACS;AAET,MAAI,UAAU,EAAG,QAAO;AAExB,QAAM,OAAO,KAAK,QAAQ,CAAC;AAG3B,MAAI,KAAK,WAAW,EAAG,QAAO;AAI9B,SACE,iBAAiB,KAAK,aAAa,KACnC,eAAe,KAAK,WAAW,KAC/B,iBAAiB,KAAK,aAAa,KACnC,gBAAgB,KAAK,WAAW,IAAI,KAAK,WAAW,IAAI;AAE5D;AAEA,SAAS,mBACP,UACA,KACA,SAOA;AACA,QAAM,EAAE,WAAW,QAAQ,UAAU,MAAM,QAAQ,IAAI;AACvD,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,UAAU,OAAO;AAAA,MACjB,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,SAAY,QAAQ;AACpB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,UAAU,OAAO;AAAA,IACjB,UAAU;AAAA,IACV;AAAA,IACA,SAAS,OAAO;AAAA,IAChB,SAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AACF;", "names": ["cast"]}