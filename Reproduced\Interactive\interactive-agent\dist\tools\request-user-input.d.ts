/**
 * Simplified request-user-input tool
 * Streamlined version of interactive-mcp tool with essential functionality only
 */
import { CapabilityInfo, ToolDefinition } from './types.js';
export interface RequestUserInputArgs {
    projectName: string;
    message: string;
    predefinedOptions?: string[];
}
export declare const requestUserInputCapability: CapabilityInfo;
export declare const requestUserInputTool: ToolDefinition<RequestUserInputArgs>;
//# sourceMappingURL=request-user-input.d.ts.map