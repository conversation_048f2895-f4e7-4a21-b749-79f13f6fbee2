# Interactive-Agent : Document de Conception et Architecture

## Vue d'ensemble du Projet

Interactive-Agent est un serveur MCP nouvelle génération conçu pour remplacer interactive-mcp avec un focus sur la performance, la stabilité et l'expérience utilisateur. Il expose un seul outil optimisé `request_user_input` avec des capacités avancées d'interaction utilisateur.

### Objectifs de Conception

- **Simplicité** : Un seul outil, une responsabilité claire
- **Performance** : Communication optimisée et gestion mémoire efficace
- **Stabilité** : Gestion robuste des erreurs et récupération automatique
- **UX Avancée** : Interface CLI moderne avec navigation intuitive
- **Maintenabilité** : Architecture modulaire et code bien structuré

## Architecture Générale

```
┌─────────────────────────────────────────────────────────────────┐
│                    CLIENT MCP (Claude Desktop, VS Code)         │
└─────────────────────┬───────────────────────────────────────────┘
                      │ MCP Protocol (JSON-RPC via stdio)
┌─────────────────────▼───────────────────────────────────────────┐
│                 INTERACTIVE-AGENT SERVER                        │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │                   main.ts (Point d'entrée)                  ││
│  │  - Configuration avancée                                    ││
│  │  - Validation des paramètres                               ││
│  │  - Initialisation serveur MCP                              ││
│  └─────────────────────────────────────────────────────────────┘│
│                                │                                │
│  ┌─────────────────────────────▼────────────────────────────────┐│
│  │                    CORE ENGINE                               ││
│  │  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐││
│  │  │   Tool Handler  │ │  Session Mgr    │ │  Config Mgr     │││
│  │  │  - Validation   │ │  - Lifecycle    │ │  - CLI Args     │││
│  │  │  - Execution    │ │  - Monitoring   │ │  - Env Vars     │││
│  │  │  - Response     │ │  - Cleanup      │ │  - Defaults     │││
│  │  └─────────────────┘ └─────────────────┘ └─────────────────┘││
│  └─────────────────────────────────────────────────────────────┘│
│                                │                                │
│  ┌─────────────────────────────▼────────────────────────────────┐│
│  │                  UI RENDERING ENGINE                         ││
│  │  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐││
│  │  │  Terminal UI    │ │   Input Handler │ │  Theme Engine   │││
│  │  │  - Rendering    │ │  - Keyboard     │ │  - Colors       │││
│  │  │  - Layout       │ │  - Navigation   │ │  - Styles       │││
│  │  │  - Animation    │ │  - Validation   │ │  - Responsive   │││
│  │  └─────────────────┘ └─────────────────┘ └─────────────────┘││
│  └─────────────────────────────────────────────────────────────┘│
│                                │                                │
│  ┌─────────────────────────────▼────────────────────────────────┐│
│  │                 COMMUNICATION LAYER                          ││
│  │  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐││
│  │  │  IPC Manager    │ │  Process Mgr    │ │  Error Handler  │││
│  │  │  - Channels     │ │  - Spawning     │ │  - Recovery     │││
│  │  │  - Serialization│ │  - Monitoring   │ │  - Logging      │││
│  │  │  - Buffering    │ │  - Cleanup      │ │  - Metrics      │││
│  │  └─────────────────┘ └─────────────────┘ └─────────────────┘││
│  └─────────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                SYSTÈME D'EXPLOITATION                           │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────────┐│
│  │   Terminal      │ │   IPC Channels  │ │   Process Monitor   ││
│  │   (Native)      │ │   (Optimized)   │ │   (Health Check)    ││
│  └─────────────────┘ └─────────────────┘ └─────────────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

## Outil MCP : request_user_input

### Spécification Technique

```typescript
interface RequestUserInputParams {
  // Paramètres obligatoires
  message: string;                    // Question à poser à l'utilisateur
  
  // Paramètres optionnels
  title?: string;                     // Titre de la session (défaut: "Interactive Agent")
  options?: string[];                 // Liste d'options prédéfinies
  allowCustomInput?: boolean;         // Autoriser saisie libre (défaut: true)
  timeout?: number;                   // Timeout en secondes (défaut: 60)
  placeholder?: string;               // Texte d'aide pour la saisie
  required?: boolean;                 // Réponse obligatoire (défaut: true)
  multiline?: boolean;                // Saisie multiligne (défaut: false)
  validation?: ValidationRule[];      // Règles de validation
  theme?: ThemeConfig;                // Configuration du thème
}

interface ValidationRule {
  type: 'regex' | 'length' | 'numeric' | 'email' | 'url' | 'custom';
  value: string | number | ((input: string) => boolean);
  message: string;
}

interface ThemeConfig {
  primaryColor?: string;
  secondaryColor?: string;
  errorColor?: string;
  successColor?: string;
  borderStyle?: 'single' | 'double' | 'round' | 'bold';
}
```

### Fonctionnalités Avancées

| Fonctionnalité | Description | Amélioration vs interactive-mcp |
|----------------|-------------|----------------------------------|
| **Navigation Fluide** | Flèches haut/bas, Page Up/Down, Home/End | Navigation rapide dans longues listes |
| **Recherche Intégrée** | Filtrage en temps réel avec `/` | Recherche instantanée dans options |
| **Validation Temps Réel** | Validation pendant la saisie | Feedback immédiat à l'utilisateur |
| **Thèmes Adaptatifs** | Détection automatique du thème terminal | Intégration visuelle optimale |
| **Saisie Multiligne** | Support Ctrl+Enter pour validation | Réponses complexes supportées |
| **Historique** | Flèches haut/bas pour historique saisie | Réutilisation des réponses précédentes |
| **Auto-complétion** | Tab pour complétion intelligente | Saisie accélérée |
| **Raccourcis Clavier** | Ctrl+C, Esc, F1-F12 configurables | Workflow utilisateur optimisé |

## Architecture des Données

### Types Principaux

```typescript
// Configuration globale du serveur
interface ServerConfig {
  name: string;
  version: string;
  timeout: number;
  maxConcurrentSessions: number;
  logLevel: LogLevel;
  theme: ThemeConfig;
  features: FeatureFlags;
}

// Session utilisateur active
interface UserSession {
  id: string;
  startTime: number;
  lastActivity: number;
  config: RequestUserInputParams;
  state: SessionState;
  process?: ChildProcess;
  ipcChannel?: IPCChannel;
  metrics: SessionMetrics;
}

// État de session
enum SessionState {
  INITIALIZING = 'initializing',
  ACTIVE = 'active',
  WAITING_INPUT = 'waiting_input',
  VALIDATING = 'validating',
  COMPLETED = 'completed',
  TIMEOUT = 'timeout',
  ERROR = 'error',
  CANCELLED = 'cancelled'
}

// Métriques de session
interface SessionMetrics {
  responseTime?: number;
  keystrokes: number;
  validationAttempts: number;
  navigationActions: number;
  searchQueries: string[];
}

// Canal de communication IPC
interface IPCChannel {
  send(message: IPCMessage): Promise<void>;
  receive(): AsyncIterator<IPCMessage>;
  close(): Promise<void>;
  isAlive(): boolean;
}

// Message IPC typé
interface IPCMessage {
  type: 'question' | 'response' | 'error' | 'heartbeat' | 'config';
  payload: any;
  timestamp: number;
  sessionId: string;
}
```

## Améliorations de Performance

### 1. Communication IPC Optimisée

```
Ancien système (interactive-mcp):
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Server    │───▶│   Files     │◀───│     UI      │
│             │    │  (polling)  │    │             │
└─────────────┘    └─────────────┘    └─────────────┘
   Latence: ~100ms     Overhead: High    CPU: Medium

Nouveau système (interactive-agent):
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Server    │◀──▶│ IPC Channel │◀──▶│     UI      │
│             │    │ (WebSocket) │    │             │
└─────────────┘    └─────────────┘    └─────────────┘
   Latence: ~5ms      Overhead: Low     CPU: Low
```

### 2. Gestion Mémoire

| Composant | interactive-mcp | interactive-agent | Amélioration |
|-----------|-----------------|-------------------|--------------|
| **Processus UI** | Spawn par demande | Pool de processus | -60% overhead |
| **Fichiers temp** | Création/suppression | Réutilisation | -80% I/O |
| **Polling** | Intervalle fixe 100ms | Event-driven | -90% CPU |
| **Mémoire** | Pas de limite | Limite configurable | Stabilité |

### 3. Optimisations Réseau

```typescript
// Configuration IPC optimisée
interface IPCConfig {
  protocol: 'websocket' | 'unix-socket' | 'named-pipe';
  bufferSize: number;           // Taille du buffer (défaut: 64KB)
  compression: boolean;         // Compression des messages
  keepAlive: number;           // Intervalle keep-alive (ms)
  maxMessageSize: number;      // Taille max message (défaut: 1MB)
  binaryMode: boolean;         // Mode binaire pour performance
}
```

## Interface CLI Avancée

### Composants UI Modernes

```
┌─────────────────────────────────────────────────────────────────┐
│ 🤖 Interactive Agent v2.0.0                    [Session: abc123] │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ ❓ Quelle action souhaitez-vous effectuer ?                     │
│                                                                 │
│ 📋 Options disponibles:                                        │
│                                                                 │
│   ▶ 🔧 Configurer le projet                                    │
│     📁 Créer un nouveau fichier                                │
│     🗑️  Supprimer des fichiers                                 │
│     🔍 Rechercher dans le code                                 │
│     ⚙️  Paramètres avancés                                     │
│                                                                 │
│ 💡 Tapez pour rechercher, ↑↓ pour naviguer, ⏎ pour valider    │
│                                                                 │
│ 🔍 Recherche: [                                              ] │
│                                                                 │
├─────────────────────────────────────────────────────────────────┤
│ ⏱️  Temps restant: 45s  │  🎯 Session: active  │  📊 Métriques │
└─────────────────────────────────────────────────────────────────┘
```

### Fonctionnalités d'Interface

#### Navigation Avancée
```typescript
interface NavigationConfig {
  // Navigation de base
  up: string[];           // ['ArrowUp', 'k']
  down: string[];         // ['ArrowDown', 'j']
  select: string[];       // ['Enter', 'Space']
  cancel: string[];       // ['Escape', 'Ctrl+C']
  
  // Navigation rapide
  pageUp: string[];       // ['PageUp', 'Ctrl+U']
  pageDown: string[];     // ['PageDown', 'Ctrl+D']
  home: string[];         // ['Home', 'g']
  end: string[];          // ['End', 'G']
  
  // Fonctions avancées
  search: string[];       // ['/']
  filter: string[];       // ['Ctrl+F']
  help: string[];         // ['F1', '?']
  multiSelect: string[];  // ['Ctrl+Space']
}
```

#### Thèmes et Styles
```typescript
interface ThemeDefinition {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    success: string;
    warning: string;
    error: string;
    info: string;
    background: string;
    foreground: string;
    muted: string;
  };
  
  borders: {
    style: BorderStyle;
    corners: 'sharp' | 'rounded';
    thickness: 'thin' | 'thick';
  };
  
  icons: {
    question: string;
    option: string;
    selected: string;
    error: string;
    success: string;
    loading: string;
  };
  
  animations: {
    enabled: boolean;
    duration: number;
    easing: 'linear' | 'ease' | 'ease-in' | 'ease-out';
  };
}
```

## Gestion des Erreurs et Stabilité

### Stratégies de Récupération

```mermaid
graph TD
    A[Erreur Détectée] --> B{Type d'Erreur}
    
    B -->|Process Crash| C[Redémarrage Automatique]
    B -->|IPC Failure| D[Reconnexion IPC]
    B -->|Validation Error| E[Retry avec Feedback]
    B -->|Timeout| F[Extension ou Annulation]
    B -->|Memory Leak| G[Garbage Collection]
    
    C --> H[Restaurer État Session]
    D --> I[Rétablir Communication]
    E --> J[Afficher Message d'Aide]
    F --> K[Proposer Options]
    G --> L[Optimiser Mémoire]
    
    H --> M[Session Restaurée]
    I --> M
    J --> N[Nouvelle Tentative]
    K --> O[Action Utilisateur]
    L --> M
    
    N --> P[Continuer]
    O --> P
    M --> P
```

### Mécanismes de Monitoring

| Métrique | Seuil Critique | Action Automatique |
|----------|----------------|-------------------|
| **Mémoire Process** | > 100MB | Redémarrage process UI |
| **Latence IPC** | > 500ms | Reconnexion canal |
| **Sessions Actives** | > 10 | Limitation nouvelles sessions |
| **Erreurs/min** | > 5 | Mode dégradé |
| **CPU Usage** | > 80% | Throttling |

## Diagramme de Séquence - Flux Optimisé

```mermaid
sequenceDiagram
    participant LLM as Client MCP
    participant Server as Interactive-Agent
    participant Pool as Process Pool
    participant UI as Interface UI
    participant IPC as Canal IPC
    
    LLM->>Server: request_user_input(params)
    Server->>Server: Valider paramètres
    Server->>Pool: Obtenir processus disponible
    Pool->>UI: Réutiliser/Créer processus UI
    Server->>IPC: Établir canal communication
    IPC->>UI: Envoyer configuration
    UI->>UI: Initialiser interface
    UI->>IPC: Confirmer prêt
    IPC->>Server: Session active
    
    loop Interaction Utilisateur
        UI->>UI: Afficher question/options
        UI->>UI: Capturer input utilisateur
        UI->>UI: Validation temps réel
        Note over UI: Navigation, recherche, saisie
    end
    
    UI->>IPC: Envoyer réponse finale
    IPC->>Server: Transmettre réponse
    Server->>Pool: Libérer processus (réutilisation)
    Server->>LLM: Retourner réponse + métriques
```

## Plan de Développement

### Phase 1 : Infrastructure Core (4 semaines)

#### Semaine 1-2 : Architecture de Base
- [ ] Configuration du projet TypeScript avec build optimisé
- [ ] Implémentation du serveur MCP avec SDK officiel
- [ ] Système de configuration avancé (CLI + env + fichier)
- [ ] Logging structuré avec rotation et niveaux
- [ ] Tests unitaires pour les composants core

#### Semaine 3-4 : Communication IPC
- [ ] Implémentation du système IPC WebSocket
- [ ] Pool de processus UI avec réutilisation
- [ ] Mécanismes de heartbeat et monitoring
- [ ] Gestion des erreurs et récupération automatique
- [ ] Tests d'intégration IPC

### Phase 2 : Interface Utilisateur (3 semaines)

#### Semaine 5-6 : Composants UI de Base
- [ ] Moteur de rendu terminal avec React/Ink optimisé
- [ ] Composant de navigation avec flèches et raccourcis
- [ ] Système de thèmes adaptatifs
- [ ] Gestion des layouts responsifs
- [ ] Animation et transitions fluides

#### Semaine 7 : Fonctionnalités Avancées
- [ ] Recherche et filtrage en temps réel
- [ ] Validation avec feedback visuel
- [ ] Support saisie multiligne
- [ ] Historique et auto-complétion
- [ ] Tests UI automatisés

### Phase 3 : Optimisation et Stabilité (2 semaines)

#### Semaine 8 : Performance
- [ ] Optimisation mémoire et CPU
- [ ] Compression des messages IPC
- [ ] Cache intelligent des ressources
- [ ] Profiling et benchmarks
- [ ] Tests de charge

#### Semaine 9 : Finalisation
- [ ] Documentation complète
- [ ] Tests end-to-end
- [ ] Packaging et distribution
- [ ] Migration depuis interactive-mcp
- [ ] Déploiement et monitoring

## Structure du Projet

```
interactive-agent/
├── src/
│   ├── core/
│   │   ├── server.ts              # Serveur MCP principal
│   │   ├── config.ts              # Gestion configuration
│   │   ├── session-manager.ts     # Gestionnaire de sessions
│   │   └── tool-handler.ts        # Handler de l'outil request_user_input
│   ├── ipc/
│   │   ├── channel.ts             # Canal IPC WebSocket
│   │   ├── message.ts             # Types et sérialisation messages
│   │   ├── pool.ts                # Pool de processus UI
│   │   └── monitor.ts             # Monitoring et health checks
│   ├── ui/
│   │   ├── app.tsx                # Application React/Ink principale
│   │   ├── components/
│   │   │   ├── question.tsx       # Composant question
│   │   │   ├── options.tsx        # Liste d'options navigable
│   │   │   ├── input.tsx          # Champ de saisie avancé
│   │   │   ├── search.tsx         # Barre de recherche
│   │   │   ├── progress.tsx       # Barre de progression
│   │   │   └── status.tsx         # Barre de statut
│   │   ├── themes/
│   │   │   ├── default.ts         # Thème par défaut
│   │   │   ├── dark.ts            # Thème sombre
│   │   │   ├── light.ts           # Thème clair
│   │   │   └── adaptive.ts        # Détection automatique
│   │   ├── hooks/
│   │   │   ├── use-navigation.ts  # Hook navigation clavier
│   │   │   ├── use-search.ts      # Hook recherche/filtrage
│   │   │   ├── use-validation.ts  # Hook validation temps réel
│   │   │   └── use-ipc.ts         # Hook communication IPC
│   │   └── utils/
│   │       ├── keyboard.ts        # Gestion événements clavier
│   │       ├── layout.ts          # Calculs de layout
│   │       └── animation.ts       # Système d'animations
│   ├── utils/
│   │   ├── logger.ts              # Système de logging
│   │   ├── errors.ts              # Classes d'erreurs personnalisées
│   │   ├── metrics.ts             # Collecte de métriques
│   │   └── validation.ts          # Règles de validation
│   ├── types/
│   │   ├── config.ts              # Types de configuration
│   │   ├── session.ts             # Types de session
│   │   ├── ipc.ts                 # Types IPC
│   │   └── ui.ts                  # Types UI
│   └── main.ts                    # Point d'entrée principal
├── tests/
│   ├── unit/                      # Tests unitaires
│   ├── integration/               # Tests d'intégration
│   ├── e2e/                       # Tests end-to-end
│   └── performance/               # Tests de performance
├── docs/
│   ├── api.md                     # Documentation API
│   ├── configuration.md           # Guide de configuration
│   ├── development.md             # Guide de développement
│   └── migration.md               # Guide de migration
├── scripts/
│   ├── build.ts                   # Script de build
│   ├── test.ts                    # Script de tests
│   └── benchmark.ts               # Script de benchmarks
├── package.json
├── tsconfig.json
├── jest.config.js
├── eslint.config.js
└── README.md
```

## Configuration Avancée

### Fichier de Configuration

```typescript
// interactive-agent.config.ts
export default {
  server: {
    name: 'interactive-agent',
    version: '2.0.0',
    timeout: 60,
    maxConcurrentSessions: 10,
    logLevel: 'info' as LogLevel,
  },

  ipc: {
    protocol: 'websocket' as IPCProtocol,
    port: 0, // Port automatique
    bufferSize: 65536,
    compression: true,
    keepAlive: 5000,
    maxMessageSize: 1048576,
  },

  ui: {
    theme: 'adaptive' as ThemeName,
    animations: true,
    responsiveLayout: true,
    keyboardShortcuts: true,
  },

  performance: {
    processPoolSize: 3,
    memoryLimit: 104857600, // 100MB
    cpuThrottling: 80,
    garbageCollection: true,
  },

  features: {
    search: true,
    multiline: true,
    validation: true,
    history: true,
    autoComplete: true,
  },
} satisfies ServerConfig;
```

### Variables d'Environnement

| Variable | Description | Défaut |
|----------|-------------|--------|
| `IA_LOG_LEVEL` | Niveau de logging | `info` |
| `IA_THEME` | Thème par défaut | `adaptive` |
| `IA_TIMEOUT` | Timeout par défaut (s) | `60` |
| `IA_MAX_SESSIONS` | Sessions max simultanées | `10` |
| `IA_MEMORY_LIMIT` | Limite mémoire (bytes) | `104857600` |
| `IA_DISABLE_ANIMATIONS` | Désactiver animations | `false` |
| `IA_IPC_PROTOCOL` | Protocole IPC | `websocket` |

## Métriques et Monitoring

### Métriques Collectées

```typescript
interface SystemMetrics {
  // Performance
  responseTime: number;           // Temps de réponse moyen (ms)
  throughput: number;             // Requêtes/seconde
  memoryUsage: number;            // Utilisation mémoire (bytes)
  cpuUsage: number;               // Utilisation CPU (%)

  // Sessions
  activeSessions: number;         // Sessions actives
  totalSessions: number;          // Total sessions créées
  sessionDuration: number;        // Durée moyenne session (ms)

  // Erreurs
  errorRate: number;              // Taux d'erreur (%)
  timeoutRate: number;            // Taux de timeout (%)
  recoveryTime: number;           // Temps de récupération moyen (ms)

  // UI
  keystrokes: number;             // Frappes clavier totales
  navigationActions: number;      // Actions de navigation
  searchQueries: number;          // Requêtes de recherche
  validationErrors: number;       // Erreurs de validation
}
```

### Dashboard de Monitoring

```
┌─────────────────────────────────────────────────────────────────┐
│ 📊 Interactive-Agent Monitoring Dashboard                      │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ 🚀 Performance                    📈 Sessions                   │
│   Response Time: 12ms              Active: 3/10                │
│   Throughput: 45 req/s             Total: 1,247               │
│   Memory: 45MB/100MB               Avg Duration: 2.3min        │
│   CPU: 15%                                                     │
│                                                                 │
│ ⚠️  Erreurs                       🎯 UI Metrics                │
│   Error Rate: 0.2%                Keystrokes: 15,432          │
│   Timeout Rate: 1.1%              Navigation: 3,421           │
│   Recovery Time: 150ms            Searches: 892               │
│                                   Validation Errors: 23       │
│                                                                 │
│ 📊 Graphiques temps réel: [████████████████████████████████] │
└─────────────────────────────────────────────────────────────────┘
```

## Migration depuis interactive-mcp

### Stratégie de Migration

1. **Compatibilité API** : Maintien de l'interface MCP existante
2. **Migration Progressive** : Déploiement côte à côte
3. **Mapping des Fonctionnalités** : Correspondance 1:1 des capacités
4. **Tests de Régression** : Validation du comportement existant

### Script de Migration

```bash
#!/bin/bash
# migrate-to-interactive-agent.sh

echo "🔄 Migration vers Interactive-Agent..."

# Sauvegarde configuration existante
cp ~/.config/interactive-mcp/config.json ~/.config/interactive-mcp/config.json.bak

# Installation interactive-agent
npm install -g interactive-agent

# Migration configuration
interactive-agent migrate --from interactive-mcp --config ~/.config/interactive-mcp/config.json

# Test de compatibilité
interactive-agent test --compatibility-mode

echo "✅ Migration terminée avec succès!"
```

Cette conception complète fournit une feuille de route détaillée pour développer interactive-agent, un serveur MCP nouvelle génération optimisé pour la performance, la stabilité et l'expérience utilisateur.
