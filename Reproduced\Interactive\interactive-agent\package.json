{"name": "interactive-agent", "version": "1.0.0", "description": "Interactive agent with minimal dependencies and optimized TypeScript configuration", "type": "module", "main": "dist/index.js", "bin": {"interactive-agent": "dist/index.js"}, "scripts": {"build": "tsc && tsc-alias", "start": "node dist/index.js", "dev": "tsc --watch", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "pino": "^8.17.2", "zod": "^3.22.4", "zod-to-json-schema": "^3.24.6"}, "devDependencies": {"@types/node": "^20.10.6", "tsc-alias": "^1.8.8", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["interactive", "agent", "typescript", "mcp"], "author": "", "license": "MIT"}