## Interaction with User

- When you want to ask a question in the middle of your thinking, you could use the MCP tool request_user_input
- Always ask for clarification if you feel uncertain in your response
- Always ask for confirmation if you feel uncertain in your response
- Feel free to ask for more information if you need it

## AI Notes - Cursor Documentation System

- Always read .notes/README.md
- Update the files in .notes folder when you learn something
- when user input "!rmb", do a self reflection and store the key knowledge in .notes folder
