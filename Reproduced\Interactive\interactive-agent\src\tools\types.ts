/**
 * Simplified TypeScript type definitions for interactive-agent
 * Essential types for request-user-input tool only
 */

import { z } from 'zod';

// Performance-optimized type helpers
export type SimpleRecord = Record<string, unknown>;
export type ToolName = string;

// Essential content type for text-only tool results
export interface ToolTextContent {
  type: 'text';
  text: string;
}

export type ToolContent = ToolTextContent;

// Main tool result interface for text-only results
export interface ToolResult {
  content: ToolTextContent[];
  isError?: boolean;
  _meta?: Record<string, unknown>;
}

// Specific result types for common use cases
export interface SuccessToolResult extends ToolResult {
  content: ToolTextContent[];
  isError?: false;
}

export interface ErrorToolResult extends ToolResult {
  content: ToolTextContent[];
  isError: true;
}

// Simplified tool registration description - just a string for performance
export type ToolRegistrationDescription = string;

// Enhanced tool handler types for better type safety
export type ToolHandler<TInput = SimpleRecord, <PERSON><PERSON><PERSON><PERSON> extends ToolResult = ToolResult> =
  (args: TInput) => Promise<TResult>;

export type SuccessToolHandler<TInput = SimpleRecord> =
  (args: TInput) => Promise<SuccessToolResult>;

export type ErrorAwareToolHandler<TInput = SimpleRecord> =
  (args: TInput) => Promise<SuccessToolResult | ErrorToolResult>;

// Enhanced tool definition interface with better type safety
export interface ToolDefinition<TInput = SimpleRecord, TResult extends ToolResult = ToolResult> {
  readonly name: ToolName;
  readonly description: ToolRegistrationDescription;
  readonly inputSchema: z.ZodSchema<TInput>;
  readonly handler: ToolHandler<TInput, TResult>;
}

// Convenience type aliases for common tool definition patterns
export type SimpleToolDefinition = ToolDefinition<SimpleRecord, ToolResult>;
export type TypedToolDefinition<TInput> = ToolDefinition<TInput, ToolResult>;
export type SuccessOnlyToolDefinition<TInput = SimpleRecord> = ToolDefinition<TInput, SuccessToolResult>;

// Simplified capability info structure
export interface CapabilityInfo {
  readonly name: string;
  readonly description: string;
}

// Enhanced tool registration type with generic support
export interface ToolRegistration<TInput = SimpleRecord, TResult extends ToolResult = ToolResult> {
  readonly definition: ToolDefinition<TInput, TResult>;
  readonly capability?: CapabilityInfo;
}

// Convenience type aliases for tool registration
export type SimpleToolRegistration = ToolRegistration<SimpleRecord, ToolResult>;
export type TypedToolRegistration<TInput> = ToolRegistration<TInput, ToolResult>;

// Performance-optimized constants
export const TOOL_CATEGORIES = {
  INPUT: 'input',
  UTILITY: 'utility'
} as const;

export type ToolCategory = typeof TOOL_CATEGORIES[keyof typeof TOOL_CATEGORIES];

// Essential utility functions for creating tool results
export const ToolResultUtils = {
  /**
   * Create a successful text-only tool result
   */
  success(text: string, meta?: Record<string, unknown>): SuccessToolResult {
    return {
      content: [{ type: 'text', text }],
      isError: false,
      _meta: meta
    };
  },

  /**
   * Create an error tool result
   */
  error(errorMessage: string, meta?: Record<string, unknown>): ErrorToolResult {
    return {
      content: [{ type: 'text', text: errorMessage }],
      isError: true,
      _meta: meta
    };
  }
} as const;
