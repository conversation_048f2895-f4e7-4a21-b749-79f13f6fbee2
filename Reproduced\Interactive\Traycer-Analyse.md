# Analyse Technique - Interactive MCP Server

## Résumé Exécutif

Le projet `interactive-mcp` est un serveur MCP (Model Context Protocol) développé en TypeScript qui permet une communication interactive entre les LLM et les utilisateurs. Il fournit des outils pour demander des entrées utilisateur, envoyer des notifications et gérer des sessions de chat intensives via des interfaces en ligne de commande.

### Capacités Principales
- **Demande d'entrée utilisateur** : Interface interactive pour collecter des réponses utilisateur
- **Notifications système** : Envoi de notifications OS pour signaler la fin de tâches
- **Chat intensif** : Sessions de chat persistantes pour des interactions prolongées
- **Support multi-plateforme** : Compatible Windows, macOS et Linux

## Architecture de Haut Niveau

```
┌─────────────────────────────────────────────────────────────────┐
│                    MCP Client (LLM)                            │
└─────────────────┬───────────────────────────────────────────────┘
                  │ MCP Protocol
┌─────────────────▼───────────────────────────────────────────────┐
│              Interactive MCP Server                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Tool Definitions│  │    Commands     │  │   UI Components │ │
│  │                 │  │                 │  │                 │ │
│  │ • request_user_ │  │ • input/        │  │ • InteractiveIn │ │
│  │   input         │  │ • intensive-    │  │   put.tsx       │ │
│  │ • message_comp  │  │   chat/         │  │ • ui.tsx files  │ │
│  │   lete_notif    │  │                 │  │                 │ │
│  │ • intensive_    │  │                 │  │                 │ │
│  │   chat tools    │  │                 │  │                 │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────┬───────────────────────────────────────────────┘
                  │ Process Spawning
┌─────────────────▼───────────────────────────────────────────────┐
│                Terminal UI (React + Ink)                       │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              User Interface                                 │ │
│  │  • Question Display                                         │ │
│  │  • Option Selection                                         │ │
│  │  • Chat History                                             │ │
│  │  • Input Collection                                         │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────┬───────────────────────────────────────────────┘
                  │ File-based Communication
┌─────────────────▼───────────────────────────────────────────────┐
│                    User                                         │
└─────────────────────────────────────────────────────────────────┘
```

## Stack Technologique

### Dépendances Principales
- **@modelcontextprotocol/sdk** : SDK pour l'implémentation du protocole MCP
- **React + Ink** : Framework pour interfaces terminal
- **TypeScript** : Langage de développement principal
- **node-notifier** : Notifications système multi-plateforme

### Outils de Développement
- **tsx** : Exécution TypeScript
- **@types/node** : Types Node.js
- **typescript** : Compilateur TypeScript

## Définitions des Outils

### 1. request_user_input
**Objectif** : Demander une entrée utilisateur via une interface interactive

**Paramètres** :
- `projectName` (string, requis) : Nom du projet pour le contexte
- `message` (string, requis) : Question à poser à l'utilisateur
- `options` (array, optionnel) : Liste d'options prédéfinies

**Schéma** :
```typescript
{
  name: "request_user_input",
  description: "Request input from the user through an interactive interface",
  inputSchema: {
    type: "object",
    properties: {
      projectName: { type: "string" },
      message: { type: "string" },
      options: {
        type: "array",
        items: { type: "string" }
      }
    },
    required: ["projectName", "message"]
  }
}
```

### 2. message_complete_notification
**Objectif** : Envoyer une notification système pour signaler la fin d'une tâche

**Paramètres** :
- `projectName` (string, requis) : Nom du projet
- `message` (string, requis) : Message de notification

### 3. Outils de Chat Intensif

#### start_intensive_chat
**Objectif** : Démarrer une session de chat persistante

**Paramètres** :
- `sessionTitle` (string, requis) : Titre de la session

#### ask_intensive_chat
**Objectif** : Poser une question dans une session active

**Paramètres** :
- `sessionId` (string, requis) : ID de la session
- `question` (string, requis) : Question à poser
- `options` (array, optionnel) : Options prédéfinies

#### stop_intensive_chat
**Objectif** : Arrêter une session de chat

**Paramètres** :
- `sessionId` (string, requis) : ID de la session à arrêter

## Architecture des Composants

### Point d'Entrée (src/index.ts)
Le serveur MCP principal qui :
- Initialise le serveur avec les outils disponibles
- Gère le filtrage des outils selon les arguments
- Configure la communication stdio
- Enregistre les gestionnaires d'outils

### Gestionnaires de Commandes

#### Commands/input/
Gère les demandes d'entrée utilisateur simples :
- Spawn un processus terminal dédié
- Utilise la communication par fichiers temporaires
- Interface React/Ink pour l'interaction

#### Commands/intensive-chat/
Gère les sessions de chat persistantes :
- Maintient des sessions actives avec IDs uniques
- Communication bidirectionnelle via fichiers
- Interface chat avec historique

### Composants UI

#### InteractiveInput.tsx
Composant React principal pour l'interface utilisateur :
- Affichage des questions
- Gestion des options multiples
- Collection des réponses utilisateur
- Validation des entrées

## Flux de Données

### Flux d'Exécution des Outils
1. **Client MCP** → Demande d'outil → **Serveur MCP**
2. **Serveur MCP** → Spawn processus → **Terminal UI**
3. **Terminal UI** → Affichage → **Utilisateur**
4. **Utilisateur** → Réponse → **Terminal UI**
5. **Terminal UI** → Fichier temporaire → **Serveur MCP**
6. **Serveur MCP** → Réponse → **Client MCP**

### Communication Inter-Processus
- **Fichiers temporaires** : Échange de données entre processus
- **Arguments de ligne de commande** : Configuration des processus enfants
- **Codes de sortie** : Signalisation d'état

## Structure des Fichiers

```
interactive-mcp/
├── package.json                    # Configuration du projet
├── tsconfig.json                   # Configuration TypeScript
├── README.md                       # Documentation
└── src/
    ├── index.ts                    # Point d'entrée principal
    ├── constants.ts                # Constantes globales
    ├── tool-definitions/           # Définitions des outils MCP
    │   ├── types.ts               # Types TypeScript
    │   ├── request-user-input.ts  # Outil d'entrée utilisateur
    │   ├── message-complete-notification.ts # Notifications
    │   └── intensive-chat.ts      # Outils de chat intensif
    ├── commands/                   # Implémentations des commandes
    │   ├── input/                 # Commande d'entrée simple
    │   │   ├── index.ts          # Logique principale
    │   │   └── ui.tsx            # Interface utilisateur
    │   └── intensive-chat/        # Commande de chat intensif
    │       ├── index.ts          # Logique de session
    │       └── ui.tsx            # Interface de chat
    ├── components/                 # Composants React réutilisables
    │   └── InteractiveInput.tsx   # Composant d'entrée interactive
    └── utils/                      # Utilitaires
        └── logger.ts              # Système de logging
```

## Détails d'Implémentation Technique

### Analyse du Point d'Entrée (src/index.ts)

Le serveur MCP principal implémente les fonctionnalités suivantes :

#### Initialisation du Serveur
```typescript
const server = new Server(
  {
    name: "interactive-mcp",
    version: "1.0.0",
  },
  {
    capabilities: {
      tools: {},
    },
  }
);
```

#### Filtrage des Outils
Le serveur supporte un filtrage dynamique des outils via l'argument `--tools` :
- `--tools input` : Active uniquement l'outil `request_user_input`
- `--tools notification` : Active uniquement l'outil `message_complete_notification`
- `--tools intensive-chat` : Active les outils de chat intensif
- Sans argument : Active tous les outils

#### Enregistrement des Gestionnaires
Chaque outil est enregistré avec son gestionnaire spécifique :
```typescript
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;

  switch (name) {
    case "request_user_input":
      return await handleRequestUserInput(args);
    case "message_complete_notification":
      return await handleMessageCompleteNotification(args);
    // ... autres outils
  }
});
```

### Mécanismes de Communication

#### Communication par Fichiers Temporaires
Le système utilise des fichiers temporaires pour la communication inter-processus :

1. **Fichier de question** : Contient la question et les options
2. **Fichier de réponse** : Contient la réponse utilisateur
3. **Fichier de session** : Pour les chats intensifs (questions/réponses multiples)

#### Stratégies de Spawn Multi-Plateforme
```typescript
// Windows
spawn('cmd', ['/c', 'start', 'cmd', '/k', command], { detached: true });

// macOS
spawn('osascript', ['-e', `tell application "Terminal" to do script "${command}"`]);

// Linux
spawn('gnome-terminal', ['--', 'bash', '-c', command]);
```

### Gestion des Sessions (Chat Intensif)

#### Structure des Sessions
```typescript
interface ChatSession {
  id: string;
  title: string;
  questionFile: string;
  responseFile: string;
  active: boolean;
  createdAt: Date;
}
```

#### Cycle de Vie des Sessions
1. **Création** : `start_intensive_chat` génère un ID unique et initialise les fichiers
2. **Communication** : `ask_intensive_chat` écrit dans le fichier de question et lit la réponse
3. **Nettoyage** : `stop_intensive_chat` ferme la session et nettoie les fichiers

### Architecture des Composants UI

#### InteractiveInput.tsx
Composant React principal utilisant Ink pour l'interface terminal :

**Fonctionnalités** :
- Affichage formaté des questions
- Gestion des options multiples avec navigation clavier
- Validation des entrées utilisateur
- Gestion des états de chargement

**Hooks utilisés** :
- `useState` : Gestion de l'état local
- `useEffect` : Effets de bord et nettoyage
- `useInput` (Ink) : Gestion des entrées clavier

#### Patterns d'État
```typescript
const [selectedOption, setSelectedOption] = useState<number>(0);
const [userInput, setUserInput] = useState<string>('');
const [isSubmitted, setIsSubmitted] = useState<boolean>(false);
```

## Tableaux de Référence Complets

### Inventaire Complet des Outils

| Nom de l'Outil | Type | Paramètres Requis | Paramètres Optionnels | Description |
|----------------|------|-------------------|----------------------|-------------|
| `request_user_input` | Interaction | `projectName`, `message` | `options[]` | Demande une entrée utilisateur via interface interactive |
| `message_complete_notification` | Notification | `projectName`, `message` | - | Envoie une notification système |
| `start_intensive_chat` | Session | `sessionTitle` | - | Démarre une session de chat persistante |
| `ask_intensive_chat` | Session | `sessionId`, `question` | `options[]` | Pose une question dans une session active |
| `stop_intensive_chat` | Session | `sessionId` | - | Arrête une session de chat |

### Mapping des Fichiers et Responsabilités

| Fichier | Responsabilité Principale | Dépendances Clés |
|---------|---------------------------|------------------|
| `src/index.ts` | Point d'entrée, serveur MCP | `@modelcontextprotocol/sdk` |
| `src/constants.ts` | Constantes globales | - |
| `src/tool-definitions/types.ts` | Types TypeScript | - |
| `src/tool-definitions/request-user-input.ts` | Définition outil entrée | `types.ts` |
| `src/tool-definitions/message-complete-notification.ts` | Définition notifications | `types.ts` |
| `src/tool-definitions/intensive-chat.ts` | Définitions chat intensif | `types.ts` |
| `src/commands/input/index.ts` | Logique entrée utilisateur | `child_process`, `fs` |
| `src/commands/input/ui.tsx` | Interface entrée | `react`, `ink` |
| `src/commands/intensive-chat/index.ts` | Logique sessions chat | `child_process`, `fs` |
| `src/commands/intensive-chat/ui.tsx` | Interface chat | `react`, `ink` |
| `src/components/InteractiveInput.tsx` | Composant réutilisable | `react`, `ink` |
| `src/utils/logger.ts` | Système de logging | - |

### Options de Configuration

| Argument CLI | Valeurs Possibles | Effet | Exemple |
|-------------|-------------------|-------|---------|
| `--tools` | `input` | Active uniquement l'outil d'entrée utilisateur | `--tools input` |
| `--tools` | `notification` | Active uniquement les notifications | `--tools notification` |
| `--tools` | `intensive-chat` | Active uniquement les outils de chat | `--tools intensive-chat` |
| (aucun) | - | Active tous les outils | - |

### Dépendances et Leurs Objectifs

| Dépendance | Version | Objectif | Type |
|------------|---------|----------|------|
| `@modelcontextprotocol/sdk` | `^1.0.0` | SDK pour protocole MCP | Production |
| `react` | `^18.0.0` | Framework UI | Production |
| `ink` | `^5.0.0` | Interface terminal React | Production |
| `node-notifier` | `^10.0.0` | Notifications système | Production |
| `typescript` | `^5.0.0` | Compilateur TypeScript | Développement |
| `tsx` | `^4.0.0` | Exécution TypeScript | Développement |
| `@types/node` | `^20.0.0` | Types Node.js | Développement |

## Guide de Développement et Déploiement

### Processus de Build

#### Scripts NPM Disponibles
```json
{
  "scripts": {
    "build": "tsc",
    "start": "tsx src/index.ts",
    "dev": "tsx watch src/index.ts"
  }
}
```

#### Étapes de Build
1. **Installation des dépendances** : `npm install`
2. **Compilation TypeScript** : `npm run build`
3. **Exécution** : `npm start` ou `tsx src/index.ts`

#### Configuration TypeScript (tsconfig.json)
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  }
}
```

### Configuration Client MCP

#### Exemple de Configuration Claude Desktop
```json
{
  "mcpServers": {
    "interactive-mcp": {
      "command": "node",
      "args": ["/path/to/interactive-mcp/dist/index.js"],
      "env": {}
    }
  }
}
```

#### Configuration avec Filtrage d'Outils
```json
{
  "mcpServers": {
    "interactive-input-only": {
      "command": "node",
      "args": ["/path/to/interactive-mcp/dist/index.js", "--tools", "input"]
    },
    "interactive-notifications-only": {
      "command": "node",
      "args": ["/path/to/interactive-mcp/dist/index.js", "--tools", "notification"]
    }
  }
}
```

### Considérations Spécifiques aux Plateformes

#### Windows
- **Terminal** : Utilise `cmd` avec `start` pour ouvrir de nouvelles fenêtres
- **Notifications** : Support natif via `node-notifier`
- **Chemins** : Gestion des chemins Windows avec antislashes
- **Processus** : Spawn avec `detached: true` pour éviter l'héritage de handles

#### macOS
- **Terminal** : Utilise AppleScript pour contrôler Terminal.app
- **Notifications** : Support natif via Notification Center
- **Permissions** : Peut nécessiter des permissions pour les notifications
- **Processus** : Spawn via `osascript` pour l'intégration système

#### Linux
- **Terminal** : Utilise `gnome-terminal` (peut nécessiter adaptation pour autres DE)
- **Notifications** : Support via `libnotify`
- **Dépendances** : Peut nécessiter l'installation de `libnotify-bin`
- **Processus** : Spawn direct avec `bash`

### Gestion des Erreurs et Debugging

#### Logging
Le système utilise un logger personnalisé (`src/utils/logger.ts`) pour :
- Tracer les appels d'outils
- Déboguer les problèmes de communication inter-processus
- Monitorer les sessions de chat intensif

#### Codes d'Erreur Communs
- **ENOENT** : Commande terminal non trouvée (problème de plateforme)
- **EACCES** : Permissions insuffisantes pour les notifications
- **TIMEOUT** : Timeout de réponse utilisateur
- **INVALID_SESSION** : Session de chat inexistante ou expirée

#### Stratégies de Récupération
- **Retry automatique** : Pour les échecs de spawn de processus
- **Fallback terminal** : Utilisation de terminaux alternatifs selon la plateforme
- **Nettoyage automatique** : Suppression des fichiers temporaires en cas d'erreur

### Troubleshooting

#### Problèmes Fréquents

1. **Terminal ne s'ouvre pas**
   - Vérifier que le terminal par défaut est installé
   - Tester manuellement les commandes de spawn
   - Vérifier les permissions d'exécution

2. **Notifications ne s'affichent pas**
   - Vérifier les permissions de notification système
   - Tester `node-notifier` indépendamment
   - Vérifier la configuration du centre de notifications

3. **Sessions de chat qui ne répondent pas**
   - Vérifier l'existence des fichiers de session
   - Contrôler les permissions de lecture/écriture
   - Nettoyer les sessions orphelines

4. **Problèmes de communication MCP**
   - Vérifier la configuration du client MCP
   - Contrôler les logs du serveur
   - Tester la connectivité stdio

#### Commandes de Diagnostic
```bash
# Test du serveur MCP
echo '{"jsonrpc":"2.0","id":1,"method":"tools/list","params":{}}' | node dist/index.js

# Test des notifications
node -e "require('node-notifier').notify({title:'Test',message:'Hello'})"

# Test du spawn de terminal (Linux)
node -e "require('child_process').spawn('gnome-terminal',['--','echo','test'])"
```

### Extensions et Personnalisations

#### Ajout de Nouveaux Outils
1. Créer la définition dans `src/tool-definitions/`
2. Implémenter le gestionnaire dans `src/commands/`
3. Ajouter l'interface UI si nécessaire
4. Enregistrer dans `src/index.ts`

#### Personnalisation de l'Interface
- Modifier les composants React dans `src/components/`
- Adapter les styles Ink pour l'apparence terminal
- Ajouter de nouveaux types d'interaction

#### Support de Nouvelles Plateformes
- Étendre les stratégies de spawn dans `src/commands/`
- Ajouter la détection de plateforme
- Implémenter les commandes terminal spécifiques

## Conclusion

Le projet `interactive-mcp` représente une implémentation robuste et extensible d'un serveur MCP pour l'interaction utilisateur. Son architecture modulaire, son support multi-plateforme et ses mécanismes de communication fiables en font une solution adaptée pour l'intégration d'interactions humaines dans les workflows d'IA.

La séparation claire entre les définitions d'outils, les implémentations de commandes et les composants UI facilite la maintenance et l'extension du système. Le support des sessions de chat intensif ouvre des possibilités pour des interactions complexes et prolongées entre les LLM et les utilisateurs.
