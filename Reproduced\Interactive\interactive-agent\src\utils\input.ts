/**
 * Enhanced input handling utility
 * Supports both console-based input and platform-specific terminal spawning
 */

import * as readline from 'readline';
import * as fs from 'fs';
import * as path from 'path';
import {
  USER_INPUT_TIMEOUT_SECONDS,
  HEARTBEAT_TIMEOUT_MS,
  HEARTBEAT_INTERVAL_MS,
  SPAWN_START_TIMEOUT_MS
} from '../constants.js';
import { getPlatformInfo, spawnWithRetry, canSpawnTerminal } from '../platform/index.js';
import { tempFileManager, TempFile } from './temp-file-manager.js';
import { RetryManager, RetryPolicy, RETRY_POLICIES } from './retry-manager.js';
import { orphanManager } from './orphan-manager.js';

export interface UserInputOptions {
  projectName: string;
  message: string;
  predefinedOptions?: string[];
  useTerminalIfAvailable?: boolean;
  retryPolicy?: RetryPolicy;
  abortSignal?: AbortSignal;
}

export interface UserInputResponse {
  response: string;
  timestamp: number;
  usedTerminal?: boolean;
  sessionId?: string;
  retryCount?: number;
}

/**
 * Enhanced cleanup function to properly dispose of resources and remove event listeners
 */
function cleanup(
  rl: readline.Interface | null,
  timeout: NodeJS.Timeout | null,
  stdinErrorHandler?: (error: Error) => void
): void {
  // Clear timeout first to prevent race conditions
  if (timeout) {
    clearTimeout(timeout);
  }

  // Remove stdin error handler if it was attached
  if (stdinErrorHandler && process.stdin) {
    try {
      process.stdin.removeListener('error', stdinErrorHandler);
    } catch (removeError) {
      // Log but don't throw - we're already in cleanup
      console.warn('Warning: Error removing stdin error listener:', removeError);
    }
  }

  // Close readline interface
  if (rl) {
    try {
      // Remove all listeners before closing to prevent potential race conditions
      rl.removeAllListeners();
      rl.close();
    } catch (closeError) {
      // Log but don't throw - we're already in cleanup
      console.warn('Warning: Error closing readline interface:', closeError);
    }
  }
}

/**
 * File-based communication interface for terminal input
 */
interface TerminalFiles {
  response: TempFile;
  heartbeat: TempFile;
  options: TempFile;
}

/**
 * Enhanced terminal input using file-based communication
 */
async function getUserInputWithTerminal(
  projectName: string,
  message: string,
  predefinedOptions?: string[],
  retryPolicy?: RetryPolicy,
  abortSignal?: AbortSignal
): Promise<UserInputResponse | null> {
  let terminalFiles: TerminalFiles | null = null;
  let heartbeatInterval: NodeJS.Timeout | null = null;
  let spawnResult: any = null;

  try {
    // Create temporary files for communication
    terminalFiles = await createTerminalFiles(projectName, message, predefinedOptions);

    // Prepare terminal command
    const terminalCommand = await prepareTerminalCommand(terminalFiles);
    if (!terminalCommand) {
      console.debug('No suitable terminal command found');
      return null;
    }

    // Spawn terminal with retry logic
    const sessionId = generateSessionId();
    spawnResult = await spawnWithRetry(
      terminalCommand.command,
      terminalCommand.args,
      {
        sessionId,
        retryPolicy: retryPolicy || RETRY_POLICIES.SPAWN_RETRY_POLICY,
        spawnTimeout: SPAWN_START_TIMEOUT_MS,
        abortSignal
      }
    );

    if (!spawnResult.success || !spawnResult.process) {
      console.debug('Terminal spawn failed:', spawnResult.error);
      return null;
    }

    // Start heartbeat monitoring
    heartbeatInterval = startHeartbeatMonitoring(terminalFiles.heartbeat, spawnResult.process);

    // Wait for user response
    const response = await waitForUserResponse(
      terminalFiles.response,
      USER_INPUT_TIMEOUT_SECONDS * 1000,
      abortSignal
    );

    return {
      response: response.trim(),
      timestamp: Date.now(),
      usedTerminal: true,
      sessionId,
      retryCount: spawnResult.retryCount || 0
    };

  } catch (error) {
    console.debug('Terminal input failed:', error);
    return null;
  } finally {
    // Cleanup resources
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
    }

    if (spawnResult?.process?.pid) {
      orphanManager.killProcess(spawnResult.process.pid).catch(err => {
        console.warn('Failed to cleanup terminal process:', err);
      });
    }

    if (terminalFiles) {
      await cleanupTerminalFiles(terminalFiles);
    }
  }
}

/**
 * Create temporary files for terminal communication
 */
async function createTerminalFiles(
  projectName: string,
  message: string,
  predefinedOptions?: string[]
): Promise<TerminalFiles> {
  const optionsContent = JSON.stringify({
    projectName,
    message,
    predefinedOptions: predefinedOptions || [],
    timestamp: Date.now()
  });

  const [response, heartbeat, options] = await Promise.all([
    tempFileManager.createTempFile('response', '.txt'),
    tempFileManager.createTempFile('heartbeat', '.txt', Date.now().toString()),
    tempFileManager.createTempFile('options', '.json', optionsContent)
  ]);

  return { response, heartbeat, options };
}

/**
 * Prepare terminal command based on platform
 */
async function prepareTerminalCommand(files: TerminalFiles): Promise<{ command: string; args: string[] } | null> {
  const platformInfo = getPlatformInfo();

  // Create a simple script that reads the options and prompts for input
  const scriptContent = `
    echo "Reading options from: ${files.options.path}"
    echo "Response file: ${files.response.path}"
    echo "Heartbeat file: ${files.heartbeat.path}"

    # Update heartbeat
    echo "${Date.now()}" > "${files.heartbeat.path}"

    # Read and display options
    if [ -f "${files.options.path}" ]; then
      echo "Options file content:"
      cat "${files.options.path}"
    fi

    # Prompt for input
    echo "Please enter your response:"
    read response

    # Write response
    echo "$response" > "${files.response.path}"

    # Final heartbeat
    echo "${Date.now()}" > "${files.heartbeat.path}"
  `;

  const scriptFile = await tempFileManager.createTempFile('input-script', '.sh', scriptContent);

  switch (platformInfo.platform) {
    case 'win32':
      return {
        command: 'cmd.exe',
        args: ['/k', `bash "${scriptFile.path}"`]
      };
    case 'darwin':
      return {
        command: 'osascript',
        args: ['-e', `tell application "Terminal" to do script "bash '${scriptFile.path}'"`]
      };
    default:
      return {
        command: 'gnome-terminal',
        args: ['--', 'bash', scriptFile.path]
      };
  }
}

/**
 * Start heartbeat monitoring for terminal process
 */
function startHeartbeatMonitoring(heartbeatFile: TempFile, process: any): NodeJS.Timeout {
  return setInterval(() => {
    try {
      if (fs.existsSync(heartbeatFile.path)) {
        const heartbeatContent = fs.readFileSync(heartbeatFile.path, 'utf8');
        const lastHeartbeat = parseInt(heartbeatContent.trim());
        const now = Date.now();

        if (now - lastHeartbeat > HEARTBEAT_TIMEOUT_MS) {
          console.warn('Terminal process appears stale, killing...');
          if (process.pid) {
            orphanManager.killProcess(process.pid);
          }
        } else {
          // Update heartbeat in orphan manager
          if (process.pid) {
            orphanManager.updateHeartbeat(process.pid);
          }
        }
      }
    } catch (error) {
      console.warn('Heartbeat monitoring error:', error);
    }
  }, HEARTBEAT_INTERVAL_MS);
}

/**
 * Wait for user response in file
 */
async function waitForUserResponse(
  responseFile: TempFile,
  timeoutMs: number,
  abortSignal?: AbortSignal
): Promise<string> {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();

    const checkForResponse = () => {
      if (abortSignal?.aborted) {
        reject(new Error('Operation aborted'));
        return;
      }

      if (Date.now() - startTime > timeoutMs) {
        reject(new Error('User input timeout'));
        return;
      }

      try {
        if (fs.existsSync(responseFile.path)) {
          const content = fs.readFileSync(responseFile.path, 'utf8').trim();
          if (content) {
            resolve(content);
            return;
          }
        }
      } catch (error) {
        // File might not exist yet, continue checking
      }

      setTimeout(checkForResponse, 500);
    };

    checkForResponse();
  });
}

/**
 * Cleanup terminal files
 */
async function cleanupTerminalFiles(files: TerminalFiles): Promise<void> {
  try {
    await Promise.allSettled([
      files.response.dispose(),
      files.heartbeat.dispose(),
      files.options.dispose()
    ]);
  } catch (error) {
    console.warn('Error cleaning up terminal files:', error);
  }
}

/**
 * Generate simple session ID
 */
function generateSessionId(): string {
  return Math.random().toString(36).substring(2, 15);
}

/**
 * Enhanced getUserInput function with terminal support
 */
export async function getUserInput(
  projectName: string,
  message: string,
  predefinedOptions?: string[],
  useTerminalIfAvailable: boolean = false,
  retryPolicy?: RetryPolicy,
  abortSignal?: AbortSignal
): Promise<UserInputResponse> {
  // Try terminal input if requested and available
  if (useTerminalIfAvailable) {
    try {
      const canUseTerminal = await canSpawnTerminal();
      if (canUseTerminal) {
        const terminalResult = await getUserInputWithTerminal(
          projectName,
          message,
          predefinedOptions,
          retryPolicy,
          abortSignal
        );
        if (terminalResult) {
          return terminalResult;
        }
      }
    } catch (error) {
      console.warn('Terminal input failed, falling back to console:', error);
    }
  }

  // Fallback vers l'interface console
  // Early validation outside Promise constructor
  if (!process.stdin || !process.stdin.readable) {
    throw new Error('process.stdin is not available or not readable');
  }

  return new Promise((resolve, reject) => {
    let rl: readline.Interface | null = null;
    let timeout: NodeJS.Timeout | null = null;
    let stdinErrorHandler: ((error: Error) => void) | undefined;
    let stdinEndHandler: (() => void) | undefined;
    let isResolved = false;
    let cleanupInProgress = false;

    // Enhanced cleanup function that includes all event handler cleanup
    const enhancedCleanup = (): void => {
      // Clear timeout first to prevent race conditions
      if (timeout) {
        clearTimeout(timeout);
      }

      // Remove stdin error handler if it was attached
      if (stdinErrorHandler && process.stdin) {
        try {
          process.stdin.removeListener('error', stdinErrorHandler);
        } catch (removeError) {
          console.warn('Warning: Error removing stdin error listener:', removeError);
        }
      }

      // Remove stdin end handler if it was attached
      if (stdinEndHandler && process.stdin) {
        try {
          process.stdin.removeListener('end', stdinEndHandler);
        } catch (removeError) {
          console.warn('Warning: Error removing stdin end listener:', removeError);
        }
      }

      // Close readline interface
      if (rl) {
        try {
          // Remove all listeners before closing to prevent potential race conditions
          rl.removeAllListeners();
          rl.close();
        } catch (closeError) {
          console.warn('Warning: Error closing readline interface:', closeError);
        }
      }
    };

    // Enhanced wrapper functions that ensure cleanup on resolve/reject with race condition protection
    const safeResolve = (value: UserInputResponse) => {
      if (!isResolved && !cleanupInProgress) {
        isResolved = true;
        cleanupInProgress = true;
        enhancedCleanup();
        resolve(value);
      }
    };

    const safeReject = (error: Error) => {
      if (!isResolved && !cleanupInProgress) {
        isResolved = true;
        cleanupInProgress = true;
        enhancedCleanup();
        reject(error);
      }
    };

    try {
      rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });

      // Setup timeout with enhanced error message
      timeout = setTimeout(() => {
        safeReject(new Error(`User input timeout after ${USER_INPUT_TIMEOUT_SECONDS} seconds`));
      }, USER_INPUT_TIMEOUT_SECONDS * 1000);

      // Prepare prompt
      let prompt = `[${projectName}] ${message}`;

      if (predefinedOptions && predefinedOptions.length > 0) {
        prompt += '\nOptions:\n';
        predefinedOptions.forEach((option, index) => {
          prompt += `  ${index + 1}. ${option}\n`;
        });
      }

      prompt += '\nYour response: ';

      // Get user input with error handling for the question callback
      rl.question(prompt, (answer) => {
        try {
          safeResolve({
            response: answer.trim(),
            timestamp: Date.now(),
            usedTerminal: false
          });
        } catch (callbackError) {
          // Handle any errors that might occur in the callback
          safeReject(callbackError instanceof Error ? callbackError : new Error(String(callbackError)));
        }
      });

      // Handle readline errors with enhanced error information
      rl.on('error', (error) => {
        const enhancedError = new Error(`Readline interface error: ${error.message}`);
        // Add original error information without using cause property for compatibility
        (enhancedError as any).originalError = error;
        safeReject(enhancedError);
      });

      // Handle readline close events to detect unexpected closures
      rl.on('close', () => {
        if (!isResolved && !cleanupInProgress) {
          safeReject(new Error('Readline interface was closed unexpectedly'));
        }
      });

      // Create and attach stdin error handler
      stdinErrorHandler = (error: Error) => {
        const enhancedError = new Error(`stdin error: ${error.message}`);
        // Add original error information without using cause property for compatibility
        (enhancedError as any).originalError = error;
        safeReject(enhancedError);
      };

      process.stdin.on('error', stdinErrorHandler);

      // Handle process.stdin end events
      stdinEndHandler = () => {
        if (!isResolved && !cleanupInProgress) {
          safeReject(new Error('stdin stream ended unexpectedly'));
        }
      };

      process.stdin.once('end', stdinEndHandler);

    } catch (error) {
      safeReject(error instanceof Error ? error : new Error(String(error)));
    }
  });
}

/**
 * Enhanced getUserInput function that accepts UserInputOptions
 */
export async function getUserInputWithOptions(options: UserInputOptions): Promise<UserInputResponse> {
  return getUserInput(
    options.projectName,
    options.message,
    options.predefinedOptions,
    options.useTerminalIfAvailable,
    options.retryPolicy,
    options.abortSignal
  );
}

/**
 * Utility function to check if terminal input is available
 */
export function isTerminalInputAvailable(): boolean {
  return canSpawnTerminal();
}

/**
 * Get platform information for input handling
 */
export function getInputPlatformInfo() {
  const platformInfo = getPlatformInfo();
  return {
    platform: platformInfo.platform,
    canSpawnTerminal: platformInfo.canSpawnTerminal,
    hasDisplay: platformInfo.hasDisplay,
    isHeadless: platformInfo.isHeadless,
    availableTerminals: platformInfo.availableTerminals
  };
}
