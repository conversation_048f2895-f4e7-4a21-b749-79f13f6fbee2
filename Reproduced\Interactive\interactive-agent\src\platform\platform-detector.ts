import * as os from 'os';
import * as fs from 'fs';
import * as path from 'path';
import { spawn } from 'child_process';
import { isCommandAvailable } from './terminal-fallbacks.js';
import { PLATFORM_CACHE_TTL_MS } from '../constants.js';

export interface PlatformInfo {
  platform: NodeJS.Platform;
  isWSL: boolean;
  isMSYS: boolean;
  shell: string;
  canSpawnTerminal: boolean;
  availableTerminals: string[];
  hasDisplay: boolean;
  isHeadless: boolean;
}

export interface TerminalCapabilities {
  canSpawn: boolean;
  preferredTerminal: string | null;
  fallbackTerminals: string[];
}

// Module-level cache
let cachedPlatformInfo: PlatformInfo | null = null;
let cacheTimestamp: number = 0;

/**
 * Détecte si nous sommes dans un environnement WSL
 */
function detectWSL(): boolean {
  // Vérification des variables d'environnement WSL
  if (process.env.WSL_DISTRO_NAME || process.env.WSLENV) {
    return true;
  }

  // Vérification du fichier /proc/version (Linux uniquement)
  if (os.platform() === 'linux') {
    try {
      const versionInfo = fs.readFileSync('/proc/version', 'utf8');
      return versionInfo.toLowerCase().includes('microsoft') ||
             versionInfo.toLowerCase().includes('wsl');
    } catch (error: any) {
      // Gestion d'erreurs améliorée pour la détection WSL
      if (error.code === 'ENOENT') {
        // Fichier /proc/version non trouvé - ignore silencieusement
        // Ceci est normal sur certains systèmes non-Linux ou containers
        return false;
      } else {
        // Autres erreurs filesystem - log avec contexte pour le débogage
        console.warn(
          `WSL detection: Failed to read /proc/version - ${error.code || 'Unknown error'}: ${error.message}. ` +
          `Path: /proc/version. This may indicate permission issues or an unusual filesystem setup.`
        );
      }
    }
  }

  return false;
}

/**
 * Détecte si nous sommes dans un environnement MSYS/MinGW
 */
function detectMSYS(): boolean {
  return !!(process.env.MSYSTEM || process.env.MINGW_PREFIX);
}

/**
 * Détecte le shell disponible sur le système
 */
function detectShell(): string {
  // Windows
  if (os.platform() === 'win32') {
    return process.env.ComSpec || 'cmd.exe';
  }
  
  // Unix-like systems
  if (process.env.SHELL) {
    return process.env.SHELL;
  }
  
  // Fallback: chercher les shells communs
  const commonShells = ['/bin/bash', '/bin/sh', '/bin/zsh', '/bin/fish'];
  for (const shell of commonShells) {
    try {
      if (fs.existsSync(shell)) {
        return shell;
      }
    } catch (error: any) {
      // Gestion d'erreurs améliorée pour la détection de shell
      if (error.code === 'ENOENT') {
        // Fichier shell non trouvé - ignore silencieusement
        continue;
      } else {
        // Autres erreurs filesystem - log avec contexte
        console.warn(
          `Shell detection: Failed to check ${shell} - ${error.code || 'Unknown error'}: ${error.message}. ` +
          `This may indicate permission issues.`
        );
      }
    }
  }
  
  return '/bin/sh'; // Fallback ultime
}

/**
 * Vérifie si l'environnement a un affichage graphique
 */
function hasGraphicalDisplay(): boolean {
  // Unix-like: vérifier DISPLAY
  if (process.env.DISPLAY) {
    return true;
  }
  
  // macOS: toujours considéré comme ayant un affichage
  if (os.platform() === 'darwin') {
    return true;
  }
  
  // Windows: toujours considéré comme ayant un affichage
  if (os.platform() === 'win32') {
    return true;
  }
  
  // Wayland sur Linux
  if (process.env.WAYLAND_DISPLAY) {
    return true;
  }
  
  return false;
}

/**
 * Détecte si l'environnement est headless (sans interface graphique)
 */
function isHeadlessEnvironment(): boolean {
  // Variables d'environnement CI/CD communes
  const ciEnvVars = ['CI', 'CONTINUOUS_INTEGRATION', 'GITHUB_ACTIONS', 'GITLAB_CI', 'JENKINS_URL'];
  if (ciEnvVars.some(envVar => process.env[envVar])) {
    return true;
  }
  
  // SSH sans X11 forwarding
  if (process.env.SSH_CLIENT && !process.env.DISPLAY) {
    return true;
  }
  
  // Docker container
  try {
    if (fs.existsSync('/.dockerenv')) {
      return true;
    }
  } catch (error: any) {
    // Gestion d'erreurs améliorée pour la détection Docker
    if (error.code !== 'ENOENT') {
      // Autres erreurs filesystem - log avec contexte
      console.warn(
        `Docker detection: Failed to check /.dockerenv - ${error.code || 'Unknown error'}: ${error.message}. ` +
        `This may indicate permission issues.`
      );
    }
    // ENOENT est ignoré silencieusement (fichier non trouvé = pas Docker)
  }
  
  return false;
}



/**
 * Détecte les terminaux disponibles sur le système
 */
async function detectAvailableTerminals(): Promise<string[]> {
  const platform = os.platform();
  let candidateTerminals: string[] = [];

  if (platform === 'win32') {
    // Windows terminals
    candidateTerminals = [
      'wt.exe', // Windows Terminal
      'ConEmu64.exe',
      'ConEmu.exe',
      'cmd.exe',
      'powershell.exe'
    ];
  } else if (platform === 'darwin') {
    // macOS terminals - pour macOS, on utilise 'open' avec des applications
    candidateTerminals = ['open']; // On vérifie juste que 'open' est disponible

    // Vérification spéciale pour les applications macOS
    const macApps: string[] = [];

    // Vérifier iTerm.app
    try {
      if (fs.existsSync('/Applications/iTerm.app')) {
        macApps.push('iTerm.app');
      }
    } catch (error: any) {
      if (error.code !== 'ENOENT') {
        console.warn(
          `macOS terminal detection: Failed to check /Applications/iTerm.app - ${error.code || 'Unknown error'}: ${error.message}`
        );
      }
    }

    // Vérifier Terminal.app
    try {
      if (fs.existsSync('/Applications/Utilities/Terminal.app')) {
        macApps.push('Terminal.app');
      }
    } catch (error: any) {
      if (error.code !== 'ENOENT') {
        console.warn(
          `macOS terminal detection: Failed to check /Applications/Utilities/Terminal.app - ${error.code || 'Unknown error'}: ${error.message}`
        );
      }
    }

    // Si 'open' est disponible et qu'on a des apps, on retourne les apps
    if (await isCommandAvailable('open') && macApps.length > 0) {
      return macApps;
    }
    return [];
  } else {
    // Linux terminals
    candidateTerminals = [
      'gnome-terminal',
      'konsole',
      'xfce4-terminal',
      'mate-terminal',
      'terminator',
      'xterm',
      'urxvt'
    ];
  }

  // Tester la disponibilité de chaque terminal
  const availabilityPromises = candidateTerminals.map(async (terminal) => {
    const available = await isCommandAvailable(terminal);
    return available ? terminal : null;
  });

  const results = await Promise.all(availabilityPromises);
  return results.filter((terminal): terminal is string => terminal !== null);
}

/**
 * Détermine si le spawn de terminal est possible
 */
function canSpawnTerminal(platformInfo: Partial<PlatformInfo>): boolean {
  // Pas de spawn en environnement headless
  if (platformInfo.isHeadless) {
    return false;
  }
  
  // Pas de spawn sans affichage graphique (sauf Windows)
  if (!platformInfo.hasDisplay && os.platform() !== 'win32') {
    return false;
  }
  
  // Doit avoir au moins un terminal disponible
  return (platformInfo.availableTerminals?.length || 0) > 0;
}

/**
 * Obtient les informations de plateforme de manière asynchrone (avec cache)
 */
export async function getPlatformInfoAsync(forceRefresh: boolean = false): Promise<PlatformInfo> {
  const now = Date.now();

  // Utiliser le cache si disponible et valide
  if (!forceRefresh && cachedPlatformInfo && (now - cacheTimestamp) < PLATFORM_CACHE_TTL_MS) {
    return cachedPlatformInfo;
  }

  // Détecter les informations de plateforme
  const platform = os.platform();
  const isWSL = detectWSL();
  const isMSYS = detectMSYS();
  const shell = detectShell();
  const hasDisplay = hasGraphicalDisplay();
  const isHeadless = isHeadlessEnvironment();
  const availableTerminals = await detectAvailableTerminals();

  const platformInfo: PlatformInfo = {
    platform,
    isWSL,
    isMSYS,
    shell,
    hasDisplay,
    isHeadless,
    availableTerminals,
    canSpawnTerminal: false // Sera défini ci-dessous
  };

  platformInfo.canSpawnTerminal = canSpawnTerminal(platformInfo);

  // Mettre en cache
  cachedPlatformInfo = platformInfo;
  cacheTimestamp = now;

  return platformInfo;
}

/**
 * Obtient les informations de plateforme (version synchrone avec cache)
 * Note: Cette version utilise le cache ou une détection simplifiée pour les terminaux
 */
export function getPlatformInfo(forceRefresh: boolean = false): PlatformInfo {
  const now = Date.now();

  // Utiliser le cache si disponible et valide
  if (!forceRefresh && cachedPlatformInfo && (now - cacheTimestamp) < PLATFORM_CACHE_TTL_MS) {
    return cachedPlatformInfo;
  }

  // Si pas de cache, créer une version avec détection simplifiée des terminaux
  const platform = os.platform();
  const isWSL = detectWSL();
  const isMSYS = detectMSYS();
  const shell = detectShell();
  const hasDisplay = hasGraphicalDisplay();
  const isHeadless = isHeadlessEnvironment();

  // Détection simplifiée des terminaux pour la version synchrone
  let availableTerminals: string[] = [];
  if (platform === 'win32') {
    availableTerminals = ['cmd.exe', 'powershell.exe']; // Toujours disponibles sur Windows
  } else if (platform === 'darwin') {
    // Vérification sécurisée pour macOS Terminal
    try {
      availableTerminals = fs.existsSync('/Applications/Utilities/Terminal.app') ? ['Terminal.app'] : [];
    } catch (error: any) {
      if (error.code !== 'ENOENT') {
        console.warn(
          `macOS terminal detection (sync): Failed to check /Applications/Utilities/Terminal.app - ${error.code || 'Unknown error'}: ${error.message}`
        );
      }
      availableTerminals = []; // Fallback sécurisé
    }
  } else {
    // Linux - on assume qu'au moins xterm est disponible
    availableTerminals = ['xterm'];
  }

  const platformInfo: PlatformInfo = {
    platform,
    isWSL,
    isMSYS,
    shell,
    hasDisplay,
    isHeadless,
    availableTerminals,
    canSpawnTerminal: false // Sera défini ci-dessous
  };

  platformInfo.canSpawnTerminal = canSpawnTerminal(platformInfo);

  // Ne pas mettre en cache cette version simplifiée
  // Le cache sera mis à jour par getPlatformInfoAsync

  return platformInfo;
}

/**
 * Rafraîchit le cache de détection de plateforme de manière asynchrone
 */
export async function refreshPlatformCacheAsync(): Promise<PlatformInfo> {
  return getPlatformInfoAsync(true);
}

/**
 * Rafraîchit le cache de détection de plateforme (version synchrone)
 */
export function refreshPlatformCache(): PlatformInfo {
  return getPlatformInfo(true);
}

/**
 * Obtient les capacités de terminal pour la plateforme actuelle de manière asynchrone
 */
export async function getTerminalCapabilitiesAsync(): Promise<TerminalCapabilities> {
  const platformInfo = await getPlatformInfoAsync();

  return {
    canSpawn: platformInfo.canSpawnTerminal,
    preferredTerminal: platformInfo.availableTerminals[0] || null,
    fallbackTerminals: platformInfo.availableTerminals.slice(1)
  };
}

/**
 * Obtient les capacités de terminal pour la plateforme actuelle (version synchrone)
 */
export function getTerminalCapabilities(): TerminalCapabilities {
  const platformInfo = getPlatformInfo();

  return {
    canSpawn: platformInfo.canSpawnTerminal,
    preferredTerminal: platformInfo.availableTerminals[0] || null,
    fallbackTerminals: platformInfo.availableTerminals.slice(1)
  };
}
