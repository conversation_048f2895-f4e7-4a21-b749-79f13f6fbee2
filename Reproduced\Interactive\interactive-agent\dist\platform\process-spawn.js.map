{"version": 3, "file": "process-spawn.js", "sourceRoot": "", "sources": ["../../src/platform/process-spawn.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAkD,MAAM,eAAe,CAAC;AACtF,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,EAAE,eAAe,EAAgB,MAAM,wBAAwB,CAAC;AACvE,OAAO,EAAE,yBAAyB,EAAE,MAAM,iBAAiB,CAAC;AAgB5D;;GAEG;AACH,SAAS,gBAAgB,CAAC,GAAW;IACnC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QACpE,OAAO,GAAG,CAAC;IACb,CAAC;IAED,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC;AAChE,CAAC;AAED;;GAEG;AACH,SAAS,aAAa,CAAC,GAAW;IAChC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QAC1F,OAAO,GAAG,CAAC;IACb,CAAC;IAED,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,CAAC;AAC7C,CAAC;AAED;;;GAGG;AACH,SAAS,iBAAiB,CAAC,GAAW;IACpC,OAAO,GAAG;QACR,uFAAuF;SACtF,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;QACvB,kCAAkC;SACjC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;QACrB,kCAAkC;SACjC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;SACrB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;QACtB,2BAA2B;SAC1B,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;QACtB,2DAA2D;SAC1D,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;SACrB,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;QACrB,yEAAyE;SACxE,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC;SAC1B,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC3B,CAAC;AAED;;GAEG;AACH,SAAS,yBAAyB,CAChC,OAAe,EACf,OAAiB,EAAE,EACnB,UAAwB,EAAE;IAE1B,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IAEvC,yDAAyD;IACzD,MAAM,kBAAkB,GAAG,YAAY,CAAC,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAE9E,IAAI,kBAAkB,EAAE,CAAC;QACvB,MAAM,cAAc,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACjD,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACzD,MAAM,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,cAAc,IAAI,WAAW,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC;QAEtF,MAAM,MAAM,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,WAAW,IAAI,mBAAmB,CAAC,CAAC;QACvE,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;YAChB,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAClD,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;QAEhD,OAAO;YACL,GAAG,EAAE,QAAQ;YACb,IAAI,EAAE,MAAM;YACZ,YAAY,EAAE;gBACZ,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,IAAI;gBAClC,KAAK,EAAE,QAAQ;gBACf,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE;gBACvC,WAAW,EAAE,KAAK;aACnB;SACF,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,wBAAwB;QACxB,MAAM,cAAc,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACjD,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAE/C,OAAO;YACL,GAAG,EAAE,SAAS;YACd,IAAI,EAAE,CAAC,IAAI,EAAE,cAAc,EAAE,GAAG,WAAW,CAAC;YAC5C,YAAY,EAAE;gBACZ,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,IAAI;gBAClC,KAAK,EAAE,QAAQ;gBACf,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE;gBACvC,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,WAAW,EAAE,KAAK;aACnB;SACF,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,uBAAuB,CAC9B,OAAe,EACf,OAAiB,EAAE,EACnB,UAAwB,EAAE;IAE1B,MAAM,cAAc,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;IAC9C,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACtD,MAAM,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,cAAc,IAAI,WAAW,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC;IAEtF,gEAAgE;IAChE,oDAAoD;IAEpD,IAAI,CAAC;QACH,wCAAwC;QACxC,MAAM,OAAO,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;QAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,mBAAmB,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;QAErH,gDAAgD;QAChD,EAAE,CAAC,aAAa,CAAC,UAAU,EAAE,gBAAgB,WAAW,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;QAE/E,wDAAwD;QACxD,MAAM,WAAW,GAAG;;;2BAGG,UAAU,UAAU,UAAU;;KAEpD,CAAC;QAEF,OAAO;YACL,GAAG,EAAE,WAAW;YAChB,IAAI,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC;YACzB,YAAY,EAAE;gBACZ,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,IAAI;gBAClC,KAAK,EAAE,QAAQ;gBACf,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE;gBACvC,GAAG,EAAE,OAAO,CAAC,GAAG;aACjB;SACF,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,6DAA6D;QAC7D,OAAO,CAAC,IAAI,CAAC,kFAAkF,EAAE,KAAK,CAAC,CAAC;QAExG,+CAA+C;QAC/C,MAAM,kBAAkB,GAAG,iBAAiB,CAAC,WAAW,CAAC,CAAC;QAE1D,MAAM,WAAW,GAAG;;;qBAGH,kBAAkB;;KAElC,CAAC;QAEF,OAAO;YACL,GAAG,EAAE,WAAW;YAChB,IAAI,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC;YACzB,YAAY,EAAE;gBACZ,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,IAAI;gBAClC,KAAK,EAAE,QAAQ;gBACf,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE;gBACvC,GAAG,EAAE,OAAO,CAAC,GAAG;aACjB;SACF,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,uBAAuB,CAC9B,OAAe,EACf,OAAiB,EAAE,EACnB,UAAwB,EAAE;IAE1B,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IACvC,MAAM,kBAAkB,GAAG,YAAY,CAAC,kBAAkB,CAAC;IAE3D,MAAM,cAAc,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;IAC9C,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACtD,MAAM,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,cAAc,IAAI,WAAW,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC;IAEtF,mDAAmD;IACnD,IAAI,WAAW,GAAG,OAAO,CAAC;IAC1B,IAAI,YAAY,GAAa,EAAE,CAAC;IAEhC,IAAI,kBAAkB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;QAClD,WAAW,GAAG,gBAAgB,CAAC;QAC/B,YAAY,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;QACjD,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,YAAY,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;SAAM,IAAI,kBAAkB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;QAClD,WAAW,GAAG,SAAS,CAAC;QACxB,YAAY,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;QACjD,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,YAAY,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;SAAM,IAAI,kBAAkB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;QACzD,WAAW,GAAG,gBAAgB,CAAC;QAC/B,YAAY,GAAG,CAAC,IAAI,EAAE,YAAY,WAAW,GAAG,CAAC,CAAC;QAClD,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,YAAY,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;SAAM,CAAC;QACN,sBAAsB;QACtB,YAAY,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;QACjD,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,YAAY,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED,OAAO;QACL,GAAG,EAAE,WAAW;QAChB,IAAI,EAAE,YAAY;QAClB,YAAY,EAAE;YACZ,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,IAAI;YAClC,KAAK,EAAE,QAAQ;YACf,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE;YACvC,GAAG,EAAE,OAAO,CAAC,GAAG;SACjB;KACF,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,eAAe,CAC7B,OAAe,EACf,OAAiB,EAAE,EACnB,UAAwB,EAAE;IAE1B,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IAEvC,gDAAgD;IAChD,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;QACnC,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,kDAAkD;SAC1D,CAAC;IACJ,CAAC;IAED,IAAI,CAAC;QACH,IAAI,WAA4E,CAAC;QAEjF,QAAQ,YAAY,CAAC,QAAQ,EAAE,CAAC;YAC9B,KAAK,OAAO;gBACV,WAAW,GAAG,yBAAyB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;gBAChE,MAAM;YACR,KAAK,QAAQ;gBACX,WAAW,GAAG,uBAAuB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;gBAC9D,MAAM;YACR,SAAS,4BAA4B;gBACnC,WAAW,GAAG,uBAAuB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;gBAC9D,MAAM;QACV,CAAC;QAED,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,YAAY,CAAC,CAAC;QAExF,6BAA6B;QAC7B,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YACjC,OAAO,CAAC,KAAK,CAAC,qCAAqC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,mCAAmC;QACnC,IAAI,OAAO,CAAC,QAAQ,IAAI,YAAY,CAAC,GAAG,EAAE,CAAC;YACzC,YAAY,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC;QAED,OAAO;YACL,OAAO,EAAE,YAAY;YACrB,OAAO,EAAE,IAAI;SACd,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,qBAAqB;SACtE,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB;IACrC,MAAM,MAAM,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEjD,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACvC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC7B,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;YAC9B,OAAO,CAAC,KAAK,CAAC,CAAC;QACjB,CAAC,EAAE,yBAAyB,CAAC,CAAC;QAE9B,MAAM,CAAC,OAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YAC/B,YAAY,CAAC,OAAO,CAAC,CAAC;YACtB,OAAO,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,OAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YAC/B,YAAY,CAAC,OAAO,CAAC,CAAC;YACtB,OAAO,CAAC,KAAK,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC"}