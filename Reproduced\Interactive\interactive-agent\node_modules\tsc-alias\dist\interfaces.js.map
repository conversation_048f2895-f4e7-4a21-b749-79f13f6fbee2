{"version": 3, "file": "interfaces.js", "sourceRoot": "", "sources": ["../src/interfaces.ts"], "names": [], "mappings": "", "sourcesContent": ["import { PathCache, TrieNode } from './utils';\n\nexport interface ITSCAliasConfig {\n  replacers?: ReplacerOptions;\n  resolveFullPaths?: boolean;\n  verbose?: boolean;\n  fileExtensions?: Partial<FileExtensions>;\n}\n\nexport type PathLike = {\n  [key: string]: string[];\n};\n\nexport type StringReplacer = (importStatement: string) => string;\n\nexport interface FileExtensions {\n  inputGlob: string;\n  outputCheck: string[];\n}\n\nexport interface ITSConfig {\n  baseUrl?: string;\n  outDir?: string;\n  declarationDir?: string;\n  paths?: PathLike;\n  replacers?: ReplacerOptions;\n  resolveFullPaths?: boolean;\n  verbose?: boolean;\n  fileExtensions?: Partial<FileExtensions>;\n}\n\nexport interface IProjectConfig {\n  configFile: string;\n  baseUrl: string;\n  outDir: string;\n  configDir: string;\n  outPath: string;\n  confDirParentFolderName: string;\n  hasExtraModule: boolean;\n  configDirInOutPath: string;\n  relConfDirPathInOutPath: string;\n  pathCache: PathCache;\n  inputGlob: string;\n}\n\nexport interface IConfig extends IProjectConfig {\n  output: IOutput;\n  aliasTrie: TrieNode<Alias>;\n  replacers: AliasReplacer[];\n}\n\nexport interface ReplaceTscAliasPathsOptions {\n  configFile?: string;\n  outDir?: string;\n  declarationDir?: string;\n  watch?: boolean;\n  verbose?: boolean;\n  debug?: boolean;\n  resolveFullPaths?: boolean;\n  resolveFullExtension?: '.js' | '.mjs' | '.cjs';\n  replacers?: string[];\n  output?: IOutput;\n  aliasTrie?: TrieNode<Alias>;\n  fileExtensions?: Partial<FileExtensions>;\n}\n\nexport interface Alias {\n  shouldPrefixMatchWildly: boolean;\n  prefix: string;\n  paths: AliasPath[];\n}\n\nexport interface AliasPath {\n  basePath: string;\n  path: string;\n  isExtra: boolean;\n}\n\nexport interface AliasReplacerArguments {\n  orig: string;\n  file: string;\n  config: IConfig;\n}\n\nexport type AliasReplacer = (args: AliasReplacerArguments) => string;\n\nexport interface ReplacerOptions {\n  [key: string]: {\n    enabled: boolean;\n    file?: string;\n  };\n}\n\nexport interface IOutput {\n  /**\n   * verbose value sets if the output should act verbose.\n   */\n  verbose: boolean;\n  /**\n   * debug logs a message on the debug level.\n   * @param {string} message message to log.\n   */\n  debug: (message: string, obj?: unknown) => void;\n  /**\n   * info logs a message on the info level.\n   * @param {string} message message to log.\n   */\n  info(message: string): void;\n  /**\n   * error logs a message on the error level and may exit the process.\n   * @param {string} message message to log.\n   * @param {boolean} exitProcess if process should exit after this error.\n   */\n  error(message: string, exitProcess?: boolean): void;\n  /**\n   * clear clears the displayed logs.\n   */\n  clear(): void;\n  /**\n   * assert claim an assertion when it fails an error is logged\n   * and the process exited.\n   * @param {unknown} claim assertion that is being claimed.\n   * @param {string} message message to log when claim fails.\n   */\n  assert(claim: unknown, message: string): void;\n}\n"]}