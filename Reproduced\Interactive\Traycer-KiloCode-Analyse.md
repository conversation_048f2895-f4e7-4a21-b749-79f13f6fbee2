# Traycer - Analyse Technique du Projet Interactive MCP

## Vue d'Ensemble

### Résumé Exécutif
Interactive MCP est un serveur TypeScript implémentant le protocole Model Context Protocol (MCP) qui facilite la communication interactive entre les LLM et les utilisateurs. Le projet fournit des outils sophistiqués pour la collecte d'entrées utilisateur, la gestion des notifications et la conduite de sessions de chat intensives via des interfaces en ligne de commande.

### Architecture de Haut Niveau
```
┌─────────────────────────────────────────────────────────────┐
│                    MCP Client (LLM)                          │
└─────────────────────┬───────────────────────────────────────┘
                      │ Protocole MCP
┌─────────────────────┴───────────────────────────────────────┐
│              Interactive MCP Server                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌───────────────┐ │
│  │ Tool Definitions │  │   Commands      │  │   UI Layer   │ │
│  │                 │  │                 │  │               │ │
│  │ • User Input    │  │ • Input CLI     │  │ • React      │ │
│  │ • Notifications │  │ • Intensive     │  │ • Ink        │ │
│  │ • Intensive     │  │   Chat CLI      │  │               │ │
│  │   Chat          │  │                 │  │               │ │
│  └─────────────────┘  └─────────────────┘  └───────────────┘ │
└───────────────────────────────────────────────────────────────┘
```

### Stack Technologique
- **Langage**: TypeScript 5.x
- **Runtime**: Node.js 18+
- **Framework UI**: React 18.x avec Ink pour interfaces terminal
- **Protocole**: Model Context Protocol (MCP)
- **Build**: TypeScript Compiler (tsc)
- **Gestionnaire de paquets**: pnpm

## Analyse Détaillée de l'Architecture

### Structure des Composants

```
interactive-mcp/
├── src/
│   ├── index.ts                 # Point d'entrée principal
│   ├── constants.ts             # Constantes globales
│   ├── tool-definitions/        # Définitions des outils MCP
│   │   ├── types.ts            # Types TypeScript partagés
│   │   ├── request-user-input.ts
│   │   ├── message-complete-notification.ts
│   │   ├── intensive-chat.ts
│   ├── commands/               # Implémentations des commandes CLI
│   │   ├── input/             # Commande de saisie utilisateur
│   │   └── intensive-chat/    # Commande de chat intensif
│   ├── components/            # Composants UI réutilisables
│   └── utils/                 # Utilitaires (logging, etc.)
```

### Définitions des Outils MCP

#### 1. Request User Input
**Schéma JSON**:
```json
{
  "name": "request_user_input",
  "description": "Demande une entrée utilisateur interactive avec support des options",
  "inputSchema": {
    "type": "object",
    "properties": {
      "projectName": {
        "type": "string",
        "description": "Nom du projet pour contexte"
      },
      "message": {
        "type": "string",
        "description": "Question à poser à l'utilisateur"
      },
      "options": {
        "type": "array",
        "items": {
          "type": "string"
        },
        "description": "Options de réponse (optionnel)"
      }
    },
    "required": ["projectName", "message"]
  }
}
```

#### 2. Message Complete Notification
**Schéma JSON**:
```json
{
  "name": "message_complete_notification",
  "description": "Envoie une notification OS lorsqu'un message est complet",
  "inputSchema": {
    "type": "object",
    "properties": {
      "projectName": {
        "type": "string",
        "description": "Nom du projet"
      },
      "message": {
        "type": "string",
        "description": "Message de notification"
      }
    },
    "required": ["projectName", "message"]
  }
}
```

#### 3. Intensive Chat Tools
**Schéma JSON**:
```json
{
  "name": "start_intensive_chat",
  "description": "Démarre une session de chat intensive",
  "inputSchema": {
    "type": "object",
    "properties": {
      "sessionTitle": {
        "type": "string",
        "description": "Titre de la session"
      }
    },
    "required": ["sessionTitle"]
  }
}
```

### Implémentations de Commandes

#### Commande Input (`src/commands/input/index.ts`)
```typescript
// Flux de traitement
1. Parse arguments CLI
2. Déterminer la plateforme (Windows/Mac/Linux)
3. Lancer le processus terminal approprié
4. Afficher l'interface utilisateur React
5. Collecter la réponse
6. Retourner le résultat via fichier temporaire
```

#### Commande Intensive Chat (`src/commands/intensive-chat/index.ts`)
```typescript
// Gestion de session
1. Créer fichier de session unique
2. Démarrer processus persistant
3. Écouter les questions via fichier
4. Afficher interface chat interactive
5. Gérer la fermeture propre de session
```

## Flux de Données et Diagrammes

### Séquence d'Exécution des Outils

```mermaid
sequenceDiagram
    participant Client as MCP Client
    participant Server as Interactive MCP Server
    participant CLI as Command CLI
    participant UI as Terminal UI
    participant User as User
    participant FS as File System

    Client->>Server: request_user_input(params)
    Server->>CLI: Spawn platform-specific process
    CLI->>UI: Initialize React/Ink interface
    UI->>User: Display question with options
    User->>UI: Provide response
    UI->>FS: Write response to temp file
    CLI->>Server: Read response from file
    Server->>Client: Return user response

    Note over Server,FS: Cross-platform file-based communication
```

### Gestion des Sessions de Chat Intensif

```mermaid
sequenceDiagram
    participant Client as MCP Client
    participant Server as Server
    participant Session as Session Manager
    participant UI as Chat UI
    participant FS as Session Files

    Client->>Server: start_intensive_chat(title)
    Server->>Session: Create new session
    Session->>FS: Create session file
    Session->>UI: Launch persistent terminal session
    UI->>User: Display chat interface
    
    loop Multiple interactions
        Client->>Server: ask_intensive_chat(sessionId, question, options?)
        Server->>Session: Write question to file
        Session->>UI: Display question in chat
        User->>UI: Provide answer
        UI->>Session: Write response to session file
        Session->>Server: Read response
        Server->>Client: Return response
    end
    
    Client->>Server: stop_intensive_chat(sessionId)
    Server->>Session: Close session
    Session->>FS: Cleanup files
    Session->>UI: Signal exit
    Server->>Client: Confirm session closed
```

## Tables de Référence Complètes

### Inventaire des Outils MCP

| Nom de l'Outil | Description | Paramètres Requis | Paramètres Optionnels | Type de Retour |
|----------------|-------------|-------------------|---------------------|----------------|
| `request_user_input` | Collecte une entrée utilisateur interactive | `projectName`, `message` | `options` | `string` |
| `message_complete_notification` | Envoie une notification OS | `projectName`, `message` | - | `boolean` |
| `start_intensive_chat` | Démarre une session de chat | `sessionTitle` | - | `string` (sessionId) |
| `ask_intensive_chat` | Pose une question en session | `sessionId`, `question` | `options` | `string` |
| `stop_intensive_chat` | Termine une session | `sessionId` | - | `boolean` |

### Structure des Fichiers

| Chemin | Type | Description |
|--------|------|-------------|
| `src/index.ts` | Point d'entrée | Serveur MCP principal |
| `src/constants.ts` | Configuration | Constantes globales et chemins |
| `src/tool-definitions/` | Définitions | Schémas des outils MCP |
| `src/commands/` | CLI | Implémentations des commandes |
| `src/components/` | UI | Composants React réutilisables |
| `src/utils/` | Utilitaires | Fonctions d'aide et logging |

### Configuration et Arguments

| Option CLI | Description | Valeur par défaut |
|------------|-------------|-------------------|
| `--project-name` | Nom du projet | Requis |
| `--message` | Message/question | Requis |
| `--options` | Options de réponse | Tableau vide |
| `--session-title` | Titre de session chat | Requis |

## Détails d'Implémentation Technique

### Point d'Entrée Principal (`src/index.ts`)

```typescript
// Architecture du serveur MCP
const server = new Server(
  {
    name: 'interactive-mcp',
    version: packageJson.version,
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

// Enregistrement des outils
server.setRequestHandler(ListToolsRequestSchema, async () => ({
  tools: [
    requestUserInputTool,
    messageCompleteNotificationTool,
    intensiveChatTools
  ].filter(Boolean),
}));
```

### Communication Inter-Processus

#### Stratégie Cross-Platform
```typescript
// Windows: utilisation de cmd.exe
const windowsCommand = `start cmd /k "${command}"`;

// macOS: utilisation de Terminal.app
const macCommand = `open -a Terminal "${scriptPath}"`;

// Linux: utilisation de x-terminal-emulator
const linuxCommand = `x-terminal-emulator -e "${command}"`;
```

#### Protocole de Communication
1. **Fichiers temporaires** : `/tmp/interactive-mcp-*`
2. **Format de données** : JSON sérialisé
3. **Synchronisation** : Attente active avec timeout
4. **Nettoyage** : Suppression automatique des fichiers

### Gestion des Sessions

#### Session Intensive Chat
```typescript
interface IntensiveChatSession {
  id: string;
  title: string;
  startTime: Date;
  sessionFile: string;
  process: ChildProcess;
}
```

## Guide de Développement et Déploiement

### Processus de Build

```bash
# Installation des dépendances
pnpm install

# Build TypeScript
pnpm run build

# Mode développement
pnpm run dev
```

### Configuration Client MCP

```json
{
  "mcpServers": {
    "interactive": {
      "command": "node",
      "args": ["path/to/interactive-mcp/dist/index.js"]
    }
  }
}
```

### Considérations Plateforme

| Plateforme | Terminal | Notes |
|------------|----------|-------|
| Windows | cmd.exe | Support complet |
| macOS | Terminal.app | Support natif |
| Linux | x-terminal-emulator | Support standard |

### Dépann