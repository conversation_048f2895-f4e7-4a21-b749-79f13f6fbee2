# Module de Détection de Plateforme - Interactive Agent

Ce document décrit le nouveau module de détection de plateforme robuste et optimisé pour `interactive-agent`, qui fournit des capacités avancées de détection d'environnement et de spawn de terminal.

## Vue d'ensemble

Le module de plateforme offre :
- **Détection robuste de l'OS** avec support WSL/subsystem
- **Cache de plateforme** pour éviter les détections répétées
- **Spawn de terminal optimisé** avec fallbacks automatiques
- **Intégration transparente** avec l'architecture existante
- **Utilisation uniquement de Node.js built-ins** pour maintenir la légèreté

## Architecture

```
src/platform/
├── platform-detector.ts    # Détection de plateforme et cache
├── process-spawn.ts         # Spawn de processus optimisé
├── terminal-fallbacks.ts    # Système de fallback de terminaux
└── index.ts                # API publique et fonctions utilitaires
```

## API Principale

### Détection de Plateforme

```typescript
import { getPlatformInfo, canSpawnTerminal } from './platform/index.js';

// Obtenir les informations de plateforme (avec cache)
const platformInfo = getPlatformInfo();
console.log(platformInfo);
// {
//   platform: 'win32',
//   isWSL: false,
//   isMSYS: false,
//   shell: 'C:\\windows\\system32\\cmd.exe',
//   canSpawnTerminal: true,
//   availableTerminals: ['wt.exe', 'ConEmu64.exe', 'ConEmu.exe', 'cmd.exe'],
//   hasDisplay: true,
//   isHeadless: false
// }

// Vérification rapide des capacités
if (canSpawnTerminal()) {
  console.log('Le spawn de terminal est disponible');
}
```

### Spawn de Terminal

```typescript
import { spawnInTerminal } from './platform/index.js';

// Spawn simple
const result = spawnInTerminal('echo', ['Hello World!'], {
  windowTitle: 'Mon Application',
  cwd: '/path/to/working/dir'
});

if (result.success) {
  console.log('Terminal spawné avec PID:', result.process?.pid);
} else {
  console.error('Échec du spawn:', result.error);
}
```

### Fallbacks de Terminal

```typescript
import {
  getFallbackStrategy,
  detectAvailableTerminals,
  getPlatformInfoAsync,
  getAvailableTerminalsFromPlatform
} from './platform/index.js';

// Détecter les terminaux disponibles (avec vérification réelle)
const terminals = await detectAvailableTerminals();
terminals.forEach(terminal => {
  console.log(`${terminal.name}: ${terminal.available ? '✅' : '❌'}`);
});

// Alternative: utiliser la détection depuis platform-detector
const platformTerminals = await getAvailableTerminalsFromPlatform();
console.log('Terminaux détectés:', platformTerminals);

// Obtenir les informations complètes de plateforme
const platformInfo = await getPlatformInfoAsync();
console.log('Plateforme:', platformInfo.platform);
console.log('Terminaux disponibles:', platformInfo.availableTerminals);
console.log('Peut spawner terminal:', platformInfo.canSpawnTerminal);

// Obtenir la stratégie de fallback
const strategy = await getFallbackStrategy();
if (strategy.useConsole) {
  console.log('Utiliser la console comme fallback');
} else {
  console.log('Terminal préféré:', strategy.preferredTerminal?.name);
}
```

### Détection Réelle des Terminaux

La bibliothèque utilise une vérification réelle de la disponibilité des commandes :

```typescript
import { getPlatformInfo, getPlatformInfoAsync } from './platform/index.js';

// Version synchrone (utilise le cache ou une détection simplifiée)
const syncInfo = getPlatformInfo();
console.log('Terminaux (sync):', syncInfo.availableTerminals);

// Version asynchrone (vérification réelle avec spawn)
const asyncInfo = await getPlatformInfoAsync();
console.log('Terminaux (async):', asyncInfo.availableTerminals);
```

**Méthodes de vérification par plateforme :**
- **Windows** : Utilise `spawn('where', [terminal])` pour vérifier la disponibilité
- **Unix/Linux** : Utilise `spawn('which', [terminal])` pour vérifier la disponibilité
- **macOS** : Vérifie `open` et l'existence des applications dans `/Applications/`

**Avantages :**
- ✅ Détection précise des terminaux réellement installés
- ✅ Cache des résultats pour de meilleures performances
- ✅ Timeout de sécurité (3 secondes) pour éviter les blocages
- ✅ Gestion d'erreur robuste

## Fonctionnalités Avancées

### Cache de Plateforme

Le module utilise un cache intelligent pour éviter les détections répétées :

```typescript
import { getPlatformInfo, refreshPlatformCache } from './platform/index.js';

// Utilise le cache si disponible (TTL: 5 minutes)
const info1 = getPlatformInfo();

// Force une nouvelle détection
const info2 = refreshPlatformCache();
```

### Utilitaires de Plateforme

```typescript
import { 
  getPlatformType, 
  isWindows, 
  isMacOS, 
  isLinux, 
  isWSL,
  getPlatformSummary 
} from './platform/index.js';

// Vérifications de plateforme
if (isWindows()) {
  console.log('Environnement Windows détecté');
}

if (isWSL()) {
  console.log('Environnement WSL détecté');
}

// Résumé complet
const summary = getPlatformSummary();
console.log(`Plateforme: ${summary.type}, Terminaux: ${summary.terminalCount}`);
```

### Spawn avec Fallback Automatique

```typescript
import { spawnWithFallback } from './platform/index.js';

// Essaie le spawn normal, puis les fallbacks automatiquement
const result = await spawnWithFallback('my-command', ['arg1', 'arg2']);

if (result.success) {
  console.log(`Spawné avec ${result.usedFallback ? 'fallback' : 'méthode principale'}`);
}
```

## Intégration avec Input

Le module d'input supporte la détection de terminaux, mais l'entrée utilisateur via terminal spawné n'est pas actuellement supportée :

```typescript
import { getUserInput } from './utils/input.js';

// Input avec support terminal optionnel (actuellement non supporté)
const response = await getUserInput(
  'MonApp',
  'Entrez votre choix:',
  ['Option 1', 'Option 2'],
  true // useTerminalIfAvailable - génère un avertissement et utilise la console
);
```

**Note importante**: Le paramètre `useTerminalIfAvailable` est actuellement non supporté en raison de la complexité de la communication IPC avec les processus terminal spawnés. Quand ce paramètre est défini à `true`, un avertissement est affiché et la fonction utilise automatiquement l'entrée console comme fallback.

console.log('Réponse:', response.response);
console.log('Utilisé terminal:', response.usedTerminal);
```

## Support Multi-Plateforme

### Windows
- **Terminaux supportés**: Windows Terminal, ConEmu, cmd.exe, PowerShell
- **Détection WSL**: Automatique via variables d'environnement et `/proc/version`
- **Détection MSYS**: Support pour MSYS2/MinGW

### macOS
- **Terminaux supportés**: Terminal.app, iTerm2
- **Méthode de spawn**: AppleScript via `osascript`
- **Détection**: Toujours considéré comme ayant un affichage graphique

### Linux
- **Terminaux supportés**: gnome-terminal, konsole, xfce4-terminal, mate-terminal, xterm, urxvt
- **Détection d'affichage**: Variables `DISPLAY` et `WAYLAND_DISPLAY`
- **Environnements headless**: Détection automatique (CI/CD, SSH, Docker)

## Configuration

Les constantes de configuration sont définies dans `src/constants.ts` :

```typescript
export const TERMINAL_SPAWN_TIMEOUT_MS = 5000;
export const PLATFORM_DETECTION_TIMEOUT_MS = 3000;
export const MAX_SPAWN_RETRIES = 3;
export const SPAWN_RETRY_DELAY_MS = 1000;
export const PLATFORM_CACHE_TTL_MS = 300000; // 5 minutes
```

## Tests

Exécutez le script de test pour vérifier le fonctionnement :

```bash
# Test complet des fonctionnalités
node test-platform.cjs

# Test rapide
node -e "const p = require('./dist/platform/index.js'); console.log(p.getPlatformInfo());"
```

## Exemples d'Utilisation

### Détection d'Environnement CI/CD

```typescript
const platformInfo = getPlatformInfo();

if (platformInfo.isHeadless) {
  console.log('Environnement CI/CD détecté, utilisation de la console uniquement');
  // Utiliser l'input console
} else {
  console.log('Environnement interactif, terminaux disponibles');
  // Utiliser l'input terminal si souhaité
}
```

### Adaptation selon la Plateforme

```typescript
const platformType = getPlatformType();

switch (platformType) {
  case 'win32':
    // Logique spécifique Windows
    break;
  case 'darwin':
    // Logique spécifique macOS
    break;
  case 'linux':
    // Logique spécifique Linux
    break;
}
```

### Gestion des Erreurs de Spawn

```typescript
const result = spawnInTerminal('my-app', ['--interactive']);

if (!result.success) {
  console.warn('Spawn échoué:', result.error);
  
  // Fallback vers console
  const consoleResult = await getUserInput('App', 'Votre choix:');
  console.log('Réponse console:', consoleResult.response);
}
```

## Migration

Pour migrer du code existant :

1. **Remplacer les détections manuelles** par `getPlatformInfo()`
2. **Utiliser `canSpawnTerminal()`** au lieu de vérifications ad-hoc
3. **Adopter les fallbacks automatiques** avec `spawnWithFallback()`
4. **Mettre à jour les appels d'input** pour utiliser le nouveau paramètre `useTerminalIfAvailable`

## Performance

- **Cache intelligent** : Évite les détections répétées (TTL: 5 minutes)
- **Détection lazy** : Les terminaux ne sont testés qu'à la demande
- **Timeouts optimisés** : Évite les blocages sur les systèmes lents
- **Mémoire minimale** : Utilise uniquement les built-ins Node.js

## Limitations

- **Input terminal** : Implémentation simplifiée, nécessite IPC pour une solution complète
- **Détection de terminaux** : Basée sur la disponibilité des commandes, pas sur l'installation réelle
- **WSL** : Détection basique, pourrait nécessiter des améliorations pour des cas edge

## Contribution

Pour contribuer au module de plateforme :

1. Respecter l'architecture modulaire existante
2. Maintenir la compatibilité avec les built-ins Node.js uniquement
3. Ajouter des tests pour les nouvelles fonctionnalités
4. Documenter les changements dans ce fichier
