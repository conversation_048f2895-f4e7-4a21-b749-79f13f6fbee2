/**
 * Simplified TypeScript type definitions for interactive-agent
 * Essential types for request-user-input tool only
 */
import { z } from 'zod';
export type SimpleRecord = Record<string, unknown>;
export type ToolName = string;
export interface ToolTextContent {
    type: 'text';
    text: string;
}
export type ToolContent = ToolTextContent;
export interface ToolResult {
    content: ToolTextContent[];
    isError?: boolean;
    _meta?: Record<string, unknown>;
}
export interface SuccessToolResult extends ToolResult {
    content: ToolTextContent[];
    isError?: false;
}
export interface ErrorToolResult extends ToolResult {
    content: ToolTextContent[];
    isError: true;
}
export type ToolRegistrationDescription = string;
export type ToolHandler<TInput = SimpleRecord, TResult extends ToolResult = ToolResult> = (args: TInput) => Promise<TResult>;
export type SuccessToolHandler<TInput = SimpleRecord> = (args: TInput) => Promise<SuccessToolResult>;
export type ErrorAwareToolHandler<TInput = SimpleRecord> = (args: TInput) => Promise<SuccessToolResult | ErrorToolResult>;
export interface ToolDefinition<TInput = SimpleRecord, TResult extends ToolResult = ToolResult> {
    readonly name: ToolName;
    readonly description: ToolRegistrationDescription;
    readonly inputSchema: z.ZodSchema<TInput>;
    readonly handler: ToolHandler<TInput, TResult>;
}
export type SimpleToolDefinition = ToolDefinition<SimpleRecord, ToolResult>;
export type TypedToolDefinition<TInput> = ToolDefinition<TInput, ToolResult>;
export type SuccessOnlyToolDefinition<TInput = SimpleRecord> = ToolDefinition<TInput, SuccessToolResult>;
export interface CapabilityInfo {
    readonly name: string;
    readonly description: string;
}
export interface ToolRegistration<TInput = SimpleRecord, TResult extends ToolResult = ToolResult> {
    readonly definition: ToolDefinition<TInput, TResult>;
    readonly capability?: CapabilityInfo;
}
export type SimpleToolRegistration = ToolRegistration<SimpleRecord, ToolResult>;
export type TypedToolRegistration<TInput> = ToolRegistration<TInput, ToolResult>;
export declare const TOOL_CATEGORIES: {
    readonly INPUT: "input";
    readonly UTILITY: "utility";
};
export type ToolCategory = typeof TOOL_CATEGORIES[keyof typeof TOOL_CATEGORIES];
export declare const ToolResultUtils: {
    /**
     * Create a successful text-only tool result
     */
    readonly success: (text: string, meta?: Record<string, unknown>) => SuccessToolResult;
    /**
     * Create an error tool result
     */
    readonly error: (errorMessage: string, meta?: Record<string, unknown>) => ErrorToolResult;
};
//# sourceMappingURL=types.d.ts.map