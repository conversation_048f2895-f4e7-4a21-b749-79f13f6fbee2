#!/usr/bin/env node

/**
 * Minimal MCP server entry point for interactive-agent
 * Simplified version of interactive-mcp with only essential functionality
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import { zodToJsonSchema } from 'zod-to-json-schema';
import { requestUserInputTool, requestUserInputCapability } from './tools/request-user-input.js';

// Create server instance
const server = new Server(
  {
    name: 'interactive-agent',
    version: '1.0.0',
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

// Register the request_user_input tool
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: requestUserInputTool.name,
        description: requestUserInputTool.description,
        inputSchema: zodToJsonSchema(requestUserInputTool.inputSchema),
      },
    ],
  };
});

// Handle tool calls
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;

  if (name === requestUserInputTool.name) {
    try {
      // Call the tool handler (it will validate arguments internally)
      const result = await requestUserInputTool.handler(args as any || {});

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      return {
        content: [
          {
            type: 'text',
            text: `Error executing tool: ${errorMessage}`,
          },
        ],
        isError: true,
      };
    }
  }

  throw new Error(`Unknown tool: ${name}`);
});

// Start the server
async function main() {
  try {
    const transport = new StdioServerTransport();
    await server.connect(transport);
    // Removed console.error to avoid MCP protocol interference
    // Server is now running on stdio
  } catch (error) {
    // Only log critical startup errors if DEBUG environment variable is set
    if (process.env.DEBUG) {
      console.error('Failed to start server:', error);
    }
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  // Removed console.error to avoid MCP protocol interference
  process.exit(0);
});

process.on('SIGTERM', async () => {
  // Removed console.error to avoid MCP protocol interference
  process.exit(0);
});

// Start the server
main().catch((error) => {
  // Only log critical errors if DEBUG environment variable is set
  if (process.env.DEBUG) {
    console.error('Server error:', error);
  }
  process.exit(1);
});
