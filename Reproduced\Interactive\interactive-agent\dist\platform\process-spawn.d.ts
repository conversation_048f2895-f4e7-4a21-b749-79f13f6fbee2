import { ChildProcess } from 'child_process';
export interface SpawnOptions {
    cwd?: string;
    env?: Record<string, string>;
    detached?: boolean;
    windowTitle?: string;
    args?: string[];
}
export interface SpawnResult {
    process: ChildProcess | null;
    success: boolean;
    error?: string;
}
/**
 * Spawn un processus dans un nouveau terminal selon la plateforme
 */
export declare function spawnInTerminal(command: string, args?: string[], options?: SpawnOptions): SpawnResult;
/**
 * Teste si le spawn de terminal fonctionne sur cette plateforme
 */
export declare function testTerminalSpawn(): Promise<boolean>;
//# sourceMappingURL=process-spawn.d.ts.map