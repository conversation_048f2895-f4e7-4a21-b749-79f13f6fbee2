{"name": "queue-lit", "description": "queue-lit is a tiny queue data structure in case you `Array#push()` or `Array#shift()` on large arrays very often", "version": "1.5.2", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/joelvoss/queue-lit", "bugs": {"url": "https://github.com/joelvoss/queue-lit/issues"}, "repository": {"type": "git", "url": "git+https://github.com/joelvoss/queue-lit.git"}, "engines": {"node": ">=12"}, "type": "module", "source": "src/index.js", "main": "dist/queue-lit.cjs", "module": "dist/queue-lit.module.js", "exports": {"types": "./dist/index.d.ts", "require": "./dist/queue-lit.cjs", "import": "./dist/queue-lit.modern.js"}, "types": "dist/index.d.ts", "files": ["dist", "LICENSE"], "scripts": {"start": "./Taskfile.sh", "test": "./Taskfile.sh test", "prepublishOnly": "./Taskfile.sh build"}, "dependencies": {}, "devDependencies": {"@jvdx/core": "^3.6.0"}, "prettier": "@jvdx/prettier-config", "prettierIgnore": ["tests/", "dist/"], "eslintConfig": {"extends": "@jvdx/eslint-config"}, "eslintIgnore": ["tests/", "dist/"]}