/**
 * Essential constants for interactive-agent
 * Minimal version based on interactive-mcp constants
 */
export declare const USER_INPUT_TIMEOUT_SECONDS: 60;
export declare const TERMINAL_SPAWN_TIMEOUT_MS: 5000;
export declare const PLATFORM_DETECTION_TIMEOUT_MS: 3000;
export declare const MAX_SPAWN_RETRIES: 3;
export declare const SPAWN_RETRY_DELAY_MS: 1000;
export declare const PLATFORM_CACHE_TTL_MS: 300000;
export declare const DEFAULT_SHELL_PATHS: {
    readonly windows: readonly ["cmd.exe", "powershell.exe"];
    readonly unix: readonly ["/bin/bash", "/bin/sh", "/bin/zsh", "/bin/fish"];
};
export declare const TERMINAL_PREFERENCES: {
    readonly win32: readonly ["wt.exe", "ConEmu64.exe", "ConEmu.exe", "cmd.exe"];
    readonly darwin: readonly ["Terminal.app", "iTerm.app"];
    readonly linux: readonly ["gnome-terminal", "konsole", "xfce4-terminal", "mate-terminal", "xterm", "urxvt"];
};
export type PlatformType = 'win32' | 'darwin' | 'linux' | 'other';
//# sourceMappingURL=constants.d.ts.map