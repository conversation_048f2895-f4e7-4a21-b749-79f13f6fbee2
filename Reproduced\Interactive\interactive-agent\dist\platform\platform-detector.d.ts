export interface PlatformInfo {
    platform: NodeJS.Platform;
    isWSL: boolean;
    isMSYS: boolean;
    shell: string;
    canSpawnTerminal: boolean;
    availableTerminals: string[];
    hasDisplay: boolean;
    isHeadless: boolean;
}
export interface TerminalCapabilities {
    canSpawn: boolean;
    preferredTerminal: string | null;
    fallbackTerminals: string[];
}
/**
 * Obtient les informations de plateforme de manière asynchrone (avec cache)
 */
export declare function getPlatformInfoAsync(forceRefresh?: boolean): Promise<PlatformInfo>;
/**
 * Obtient les informations de plateforme (version synchrone avec cache)
 * Note: Cette version utilise le cache ou une détection simplifiée pour les terminaux
 */
export declare function getPlatformInfo(forceRefresh?: boolean): PlatformInfo;
/**
 * Rafraîchit le cache de détection de plateforme de manière asynchrone
 */
export declare function refreshPlatformCacheAsync(): Promise<PlatformInfo>;
/**
 * Rafraîchit le cache de détection de plateforme (version synchrone)
 */
export declare function refreshPlatformCache(): PlatformInfo;
/**
 * Obtient les capacités de terminal pour la plateforme actuelle de manière asynchrone
 */
export declare function getTerminalCapabilitiesAsync(): Promise<TerminalCapabilities>;
/**
 * Obtient les capacités de terminal pour la plateforme actuelle (version synchrone)
 */
export declare function getTerminalCapabilities(): TerminalCapabilities;
//# sourceMappingURL=platform-detector.d.ts.map