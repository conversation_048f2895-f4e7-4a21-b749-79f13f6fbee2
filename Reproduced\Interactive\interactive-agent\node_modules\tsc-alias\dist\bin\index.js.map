{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/bin/index.ts"], "names": [], "mappings": ";;;AACA,yCAA4C;AAC5C,0BAA0C;AAE1C,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAElD,mBAAO;KACJ,IAAI,CAAC,WAAW,CAAC;KACjB,OAAO,CAAC,OAAO,CAAC;KAChB,MAAM,CAAC,sBAAsB,EAAE,uBAAuB,CAAC;KACvD,MAAM,CAAC,aAAa,EAAE,sBAAsB,CAAC;KAC7C,MAAM,CACL,uBAAuB,EACvB,uFAAuF,CACxF;KACA,MAAM,CACL,0BAA0B,EAC1B,kFAAkF,CACnF;KACA,SAAS,CACR,IAAI,kBAAM,CACR,qCAAqC,EACrC,+EAA+E,CAChF;KACE,OAAO,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;KAChC,OAAO,CAAC,KAAK,CAAC,CAClB;KACA,MAAM,CACL,cAAc,EACd,sDAAsD,CACvD;KACA,MAAM,CAAC,eAAe,EAAE,gDAAgD,CAAC;KACzE,MAAM,CAAC,SAAS,EAAE,2CAA2C,CAAC;KAC9D,MAAM,CAAC,+BAA+B,EAAE,iCAAiC,CAAC;KAC1E,MAAM,CAAC,oBAAoB,EAAE,uCAAuC,CAAC;KACrE,MAAM,CACL,+BAA+B,EAC/B,oDAAoD,CACrD;KACA,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAE5B,MAAM,OAAO,GAAG,mBAAO,CAAC,IAAI,EAAE,CAAC;AAE/B,IAAA,wBAAoB,EAAC;IACnB,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;IAClD,UAAU,EAAE,OAAO,CAAC,OAAO;IAC3B,KAAK,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK;IACtB,MAAM,EAAE,OAAO,CAAC,GAAG;IACnB,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO;IAC1B,KAAK,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK;IACtB,gBAAgB,EAAE,CAAC,CAAC,OAAO,CAAC,gBAAgB;IAC5C,SAAS,EAAE,OAAO,CAAC,QAAQ;IAC3B,cAAc,EAAE;QACd,SAAS,EAAE,OAAO,CAAC,SAAS;QAC5B,WAAW,EAAE,OAAO,CAAC,WAAW;KACjC;CACF,CAAC,CAAC", "sourcesContent": ["#! /usr/bin/env node\nimport { Option, program } from 'commander';\nimport { replaceTscAliasPaths } from '..';\n\nconst { version } = require('../../package.json');\n\nprogram\n  .name('tsc-alias')\n  .version(version)\n  .option('-p, --project <file>', 'path to tsconfig.json')\n  .option('-w, --watch', 'Observe file changes')\n  .option(\n    '--outDir, --dir <dir>',\n    'Run in a folder leaving the \"outDir\" of the tsconfig.json (relative path to tsconfig)'\n  )\n  .option(\n    '-f, --resolve-full-paths',\n    'Attempt to fully resolve import paths if the corresponding .js file can be found'\n  )\n  .addOption(\n    new Option(\n      '-fe, --resolve-full-extension [ext]',\n      'Specify the extension of incomplete import paths, works with resolveFullPaths'\n    )\n      .choices(['.js', '.mjs', '.cjs'])\n      .default('.js')\n  )\n  .option(\n    '-s, --silent',\n    'Reduced terminal output (default: true) [deprecated]'\n  )\n  .option('-v, --verbose', 'Additional information is send to the terminal')\n  .option('--debug', 'Debug information is send to the terminal')\n  .option('-r, --replacer <replacers...>', 'path to optional extra replacer')\n  .option('--inputglob <glob>', 'Overwrite glob used for file scanning')\n  .option(\n    '--outputcheck <extensions...>',\n    'Overwrite file extensions used for path resolution'\n  )\n  .parseAsync(process.argv);\n\nconst options = program.opts();\n\nreplaceTscAliasPaths({\n  resolveFullExtension: options.resolveFullExtension,\n  configFile: options.project,\n  watch: !!options.watch,\n  outDir: options.dir,\n  verbose: !!options.verbose,\n  debug: !!options.debug,\n  resolveFullPaths: !!options.resolveFullPaths,\n  replacers: options.replacer,\n  fileExtensions: {\n    inputGlob: options.inputglob,\n    outputCheck: options.outputcheck\n  }\n});\n"]}