<a name="0.2.6"></a>
## [0.2.6](https://github.com/huafu/bs-logger/compare/v0.2.5...v0.2.6) (2018-11-10)



<a name="0.2.5"></a>
## [0.2.5](https://github.com/huafu/bs-logger/compare/v0.2.4...v0.2.5) (2018-09-04)



<a name="0.2.4"></a>
## [0.2.4](https://github.com/huafu/bs-logger/compare/v0.2.3...v0.2.4) (2018-09-04)



<a name="0.2.3"></a>
## [0.2.3](https://github.com/huafu/bs-logger/compare/v0.2.2...v0.2.3) (2018-08-31)


### Bug Fixes

* **formatter:** allow json formatter to serialize circular data ([103bac0](https://github.com/huafu/bs-logger/commit/103bac0))



<a name="0.2.2"></a>
## [0.2.2](https://github.com/huafu/bs-logger/compare/v0.2.1...v0.2.2) (2018-08-31)


### Bug Fixes

* **testing:** adds LoggerMock to exported symbols ([134dab8](https://github.com/huafu/bs-logger/commit/134dab8))



<a name="0.2.1"></a>
## [0.2.1](https://github.com/huafu/bs-logger/compare/v0.2.0...v0.2.1) (2018-08-31)



<a name="0.2.0"></a>
# [0.2.0](https://github.com/huafu/bs-logger/compare/v0.1.1...v0.2.0) (2018-08-31)


### Features

* **testing:** adds helpers for testing ([0e65a49](https://github.com/huafu/bs-logger/commit/0e65a49))



<a name="0.1.1"></a>
## [0.1.1](https://github.com/huafu/bs-logger/compare/02d6fc7...v0.1.1) (2018-08-30)


### Features

* adds custom formatters ([02d6fc7](https://github.com/huafu/bs-logger/commit/02d6fc7))



