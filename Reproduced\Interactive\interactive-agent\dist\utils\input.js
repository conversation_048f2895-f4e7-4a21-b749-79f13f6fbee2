/**
 * Enhanced input handling utility
 * Supports both console-based input and platform-specific terminal spawning
 */
import * as readline from 'readline';
import { USER_INPUT_TIMEOUT_SECONDS } from '../constants.js';
import { getPlatformInfo, canSpawnTerminal } from '../platform/index.js';
/**
 * Enhanced cleanup function to properly dispose of resources and remove event listeners
 */
function cleanup(rl, timeout, stdinErrorHandler) {
    // Clear timeout first to prevent race conditions
    if (timeout) {
        clearTimeout(timeout);
    }
    // Remove stdin error handler if it was attached
    if (stdinErrorHandler && process.stdin) {
        try {
            process.stdin.removeListener('error', stdinErrorHandler);
        }
        catch (removeError) {
            // Log but don't throw - we're already in cleanup
            console.warn('Warning: Error removing stdin error listener:', removeError);
        }
    }
    // Close readline interface
    if (rl) {
        try {
            // Remove all listeners before closing to prevent potential race conditions
            rl.removeAllListeners();
            rl.close();
        }
        catch (closeError) {
            // Log but don't throw - we're already in cleanup
            console.warn('Warning: Error closing readline interface:', closeError);
        }
    }
}
/**
 * Attempts to get user input using terminal spawning if available
 * NOTE: This feature is currently not supported due to the complexity of IPC communication
 * with spawned terminal processes. The function will always return null and fall back to console input.
 */
async function getUserInputWithTerminal(projectName, message, predefinedOptions) {
    // Terminal input is not currently supported due to IPC communication complexity
    console.warn('Terminal input (useTerminalIfAvailable) is not currently supported. ' +
        'This feature requires complex IPC communication with spawned terminal processes ' +
        'which is not yet implemented. Falling back to console input.');
    return null;
}
/**
 * Get user input using console-based interface with optional terminal spawning
 */
export async function getUserInput(projectName, message, predefinedOptions, useTerminalIfAvailable = false) {
    // Essayer d'abord le terminal si demandé
    if (useTerminalIfAvailable) {
        console.warn('useTerminalIfAvailable=true was requested, but terminal input is not currently supported. ' +
            'This feature requires IPC communication with spawned terminal processes. ' +
            'Using console input instead.');
        const terminalResult = await getUserInputWithTerminal(projectName, message, predefinedOptions);
        if (terminalResult) {
            return { ...terminalResult, usedTerminal: true };
        }
    }
    // Fallback vers l'interface console
    // Early validation outside Promise constructor
    if (!process.stdin || !process.stdin.readable) {
        throw new Error('process.stdin is not available or not readable');
    }
    return new Promise((resolve, reject) => {
        let rl = null;
        let timeout = null;
        let stdinErrorHandler;
        let stdinEndHandler;
        let isResolved = false;
        let cleanupInProgress = false;
        // Enhanced cleanup function that includes all event handler cleanup
        const enhancedCleanup = () => {
            // Clear timeout first to prevent race conditions
            if (timeout) {
                clearTimeout(timeout);
            }
            // Remove stdin error handler if it was attached
            if (stdinErrorHandler && process.stdin) {
                try {
                    process.stdin.removeListener('error', stdinErrorHandler);
                }
                catch (removeError) {
                    console.warn('Warning: Error removing stdin error listener:', removeError);
                }
            }
            // Remove stdin end handler if it was attached
            if (stdinEndHandler && process.stdin) {
                try {
                    process.stdin.removeListener('end', stdinEndHandler);
                }
                catch (removeError) {
                    console.warn('Warning: Error removing stdin end listener:', removeError);
                }
            }
            // Close readline interface
            if (rl) {
                try {
                    // Remove all listeners before closing to prevent potential race conditions
                    rl.removeAllListeners();
                    rl.close();
                }
                catch (closeError) {
                    console.warn('Warning: Error closing readline interface:', closeError);
                }
            }
        };
        // Enhanced wrapper functions that ensure cleanup on resolve/reject with race condition protection
        const safeResolve = (value) => {
            if (!isResolved && !cleanupInProgress) {
                isResolved = true;
                cleanupInProgress = true;
                enhancedCleanup();
                resolve(value);
            }
        };
        const safeReject = (error) => {
            if (!isResolved && !cleanupInProgress) {
                isResolved = true;
                cleanupInProgress = true;
                enhancedCleanup();
                reject(error);
            }
        };
        try {
            rl = readline.createInterface({
                input: process.stdin,
                output: process.stdout
            });
            // Setup timeout with enhanced error message
            timeout = setTimeout(() => {
                safeReject(new Error(`User input timeout after ${USER_INPUT_TIMEOUT_SECONDS} seconds`));
            }, USER_INPUT_TIMEOUT_SECONDS * 1000);
            // Prepare prompt
            let prompt = `[${projectName}] ${message}`;
            if (predefinedOptions && predefinedOptions.length > 0) {
                prompt += '\nOptions:\n';
                predefinedOptions.forEach((option, index) => {
                    prompt += `  ${index + 1}. ${option}\n`;
                });
            }
            prompt += '\nYour response: ';
            // Get user input with error handling for the question callback
            rl.question(prompt, (answer) => {
                try {
                    safeResolve({
                        response: answer.trim(),
                        timestamp: Date.now(),
                        usedTerminal: false
                    });
                }
                catch (callbackError) {
                    // Handle any errors that might occur in the callback
                    safeReject(callbackError instanceof Error ? callbackError : new Error(String(callbackError)));
                }
            });
            // Handle readline errors with enhanced error information
            rl.on('error', (error) => {
                const enhancedError = new Error(`Readline interface error: ${error.message}`);
                // Add original error information without using cause property for compatibility
                enhancedError.originalError = error;
                safeReject(enhancedError);
            });
            // Handle readline close events to detect unexpected closures
            rl.on('close', () => {
                if (!isResolved && !cleanupInProgress) {
                    safeReject(new Error('Readline interface was closed unexpectedly'));
                }
            });
            // Create and attach stdin error handler
            stdinErrorHandler = (error) => {
                const enhancedError = new Error(`stdin error: ${error.message}`);
                // Add original error information without using cause property for compatibility
                enhancedError.originalError = error;
                safeReject(enhancedError);
            };
            process.stdin.on('error', stdinErrorHandler);
            // Handle process.stdin end events
            stdinEndHandler = () => {
                if (!isResolved && !cleanupInProgress) {
                    safeReject(new Error('stdin stream ended unexpectedly'));
                }
            };
            process.stdin.once('end', stdinEndHandler);
        }
        catch (error) {
            safeReject(error instanceof Error ? error : new Error(String(error)));
        }
    });
}
/**
 * Enhanced getUserInput function that accepts UserInputOptions
 */
export async function getUserInputWithOptions(options) {
    return getUserInput(options.projectName, options.message, options.predefinedOptions, options.useTerminalIfAvailable);
}
/**
 * Utility function to check if terminal input is available
 */
export function isTerminalInputAvailable() {
    return canSpawnTerminal();
}
/**
 * Get platform information for input handling
 */
export function getInputPlatformInfo() {
    const platformInfo = getPlatformInfo();
    return {
        platform: platformInfo.platform,
        canSpawnTerminal: platformInfo.canSpawnTerminal,
        hasDisplay: platformInfo.hasDisplay,
        isHeadless: platformInfo.isHeadless,
        availableTerminals: platformInfo.availableTerminals
    };
}
//# sourceMappingURL=input.js.map