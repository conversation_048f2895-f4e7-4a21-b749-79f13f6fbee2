/**
 * Enhanced input handling utility
 * Supports both console-based input and platform-specific terminal spawning
 */
export interface UserInputOptions {
    projectName: string;
    message: string;
    predefinedOptions?: string[];
    useTerminalIfAvailable?: boolean;
}
export interface UserInputResponse {
    response: string;
    timestamp: number;
    usedTerminal?: boolean;
}
/**
 * Get user input using console-based interface with optional terminal spawning
 */
export declare function getUserInput(projectName: string, message: string, predefinedOptions?: string[], useTerminalIfAvailable?: boolean): Promise<UserInputResponse>;
/**
 * Enhanced getUserInput function that accepts UserInputOptions
 */
export declare function getUserInputWithOptions(options: UserInputOptions): Promise<UserInputResponse>;
/**
 * Utility function to check if terminal input is available
 */
export declare function isTerminalInputAvailable(): boolean;
/**
 * Get platform information for input handling
 */
export declare function getInputPlatformInfo(): {
    platform: NodeJS.Platform;
    canSpawnTerminal: boolean;
    hasDisplay: boolean;
    isHeadless: boolean;
    availableTerminals: string[];
};
//# sourceMappingURL=input.d.ts.map