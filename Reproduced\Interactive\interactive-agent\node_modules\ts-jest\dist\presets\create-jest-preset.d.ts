import type { Config } from '@jest/types';
import { DefaultEsmLegacyPreset, DefaultEsmPreset, DefaultEsmTransformOptions, DefaultLegacyPreset, DefaultPreset, DefaultTransformOptions, JsWithBabelEsmLegacyPreset, JsWithBabelEsmPreset, JsWithBabelEsmTransformOptions, JsWithBabelLegacyPreset, JsWithBabelPreset, JsWithBabelTransformerOptions, JsWithTsEsmLegacyPreset, JsWithTsEsmPreset, JsWithTsEsmTransformOptions, JsWithTsLegacyPreset, JsWithTsPreset, JsWithTsTransformOptions, TsJestPresets } from '../types';
/**
 * @deprecated use other functions below instead
 */
export declare function createJestPreset(legacy?: boolean, allowJs?: boolean, extraOptions?: Config.InitialOptions): TsJestPresets;
export declare function createDefaultPreset(tsJestTransformOptions?: DefaultTransformOptions): DefaultPreset;
export declare function createDefaultLegacyPreset(tsJestTransformOptions?: DefaultTransformOptions): DefaultLegacyPreset;
export declare function createJsWithTsPreset(tsJestTransformOptions?: JsWithTsTransformOptions): JsWithTsPreset;
export declare function createJsWithTsLegacyPreset(tsJestTransformOptions?: JsWithTsTransformOptions): JsWithTsLegacyPreset;
export declare function createJsWithBabelPreset(tsJestTransformOptions?: JsWithBabelTransformerOptions): JsWithBabelPreset;
export declare function createJsWithBabelLegacyPreset(tsJestTransformOptions?: JsWithBabelTransformerOptions): JsWithBabelLegacyPreset;
export declare function createDefaultEsmPreset(tsJestTransformOptions?: DefaultEsmTransformOptions): DefaultEsmPreset;
export declare function createDefaultEsmLegacyPreset(tsJestTransformOptions?: DefaultEsmTransformOptions): DefaultEsmLegacyPreset;
export declare function createJsWithTsEsmPreset(tsJestTransformOptions?: JsWithTsEsmTransformOptions): JsWithTsEsmPreset;
export declare function createJsWithTsEsmLegacyPreset(tsJestTransformOptions?: JsWithTsEsmTransformOptions): JsWithTsEsmLegacyPreset;
export declare function createJsWithBabelEsmPreset(tsJestTransformOptions?: JsWithBabelEsmTransformOptions): JsWithBabelEsmPreset;
export declare function createJsWithBabelEsmLegacyPreset(tsJestTransformOptions?: JsWithBabelEsmTransformOptions): JsWithBabelEsmLegacyPreset;
