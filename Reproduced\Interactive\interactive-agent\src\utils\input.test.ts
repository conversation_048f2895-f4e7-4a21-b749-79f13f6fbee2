/**
 * Tests for improved error handling in input.ts
 * Validates coordination between timeout mechanism, readline errors, and stdin errors
 */

import * as readline from 'readline';
import { EventEmitter } from 'events';
import { getUserInput } from './input';

// Mock readline module
jest.mock('readline');
const mockReadline = readline as jest.Mocked<typeof readline>;

// Mock process.stdin
const mockStdin = new EventEmitter() as any;
mockStdin.readable = true;

// Store original process.stdin
const originalStdin = process.stdin;

describe('getUserInput Error Handling', () => {
  let mockRl: any;
  let consoleWarnSpy: jest.SpyInstance;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock console.warn to capture cleanup warnings
    consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
    
    // Create mock readline interface
    mockRl = new EventEmitter();
    mockRl.question = jest.fn();
    mockRl.close = jest.fn();
    mockRl.removeAllListeners = jest.fn();
    
    // Setup readline.createInterface mock
    mockReadline.createInterface.mockReturnValue(mockRl);
    
    // Replace process.stdin with our mock
    Object.defineProperty(process, 'stdin', {
      value: mockStdin,
      writable: true,
      configurable: true
    });
    
    // Clear all listeners from mock stdin
    mockStdin.removeAllListeners();
  });

  afterEach(() => {
    // Restore original process.stdin
    Object.defineProperty(process, 'stdin', {
      value: originalStdin,
      writable: true,
      configurable: true
    });
    
    consoleWarnSpy.mockRestore();
    
    // Clear any remaining timers
    jest.clearAllTimers();
  });

  beforeAll(() => {
    jest.useFakeTimers();
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  test('should handle successful user input', async () => {
    const inputPromise = getUserInput('TestProject', 'Enter something');
    
    // Simulate user input
    const questionCallback = mockRl.question.mock.calls[0][1];
    questionCallback('test response');
    
    const result = await inputPromise;
    
    expect(result.response).toBe('test response');
    expect(result.timestamp).toBeGreaterThan(0);
    expect(mockRl.close).toHaveBeenCalled();
    expect(mockRl.removeAllListeners).toHaveBeenCalled();
  });

  test('should handle timeout correctly', async () => {
    const inputPromise = getUserInput('TestProject', 'Enter something');
    
    // Fast-forward time to trigger timeout
    jest.advanceTimersByTime(60000);
    
    await expect(inputPromise).rejects.toThrow('User input timeout after 60 seconds');
    expect(mockRl.close).toHaveBeenCalled();
    expect(mockRl.removeAllListeners).toHaveBeenCalled();
  });

  test('should handle readline errors', async () => {
    const inputPromise = getUserInput('TestProject', 'Enter something');
    
    // Simulate readline error
    const testError = new Error('Readline test error');
    mockRl.emit('error', testError);
    
    await expect(inputPromise).rejects.toThrow('Readline interface error: Readline test error');
    expect(mockRl.close).toHaveBeenCalled();
    expect(mockRl.removeAllListeners).toHaveBeenCalled();
  });

  test('should handle stdin errors', async () => {
    const inputPromise = getUserInput('TestProject', 'Enter something');
    
    // Simulate stdin error
    const testError = new Error('Stdin test error');
    mockStdin.emit('error', testError);
    
    await expect(inputPromise).rejects.toThrow('stdin error: Stdin test error');
    expect(mockRl.close).toHaveBeenCalled();
    expect(mockRl.removeAllListeners).toHaveBeenCalled();
  });

  test('should handle unexpected readline close', async () => {
    const inputPromise = getUserInput('TestProject', 'Enter something');
    
    // Simulate unexpected close
    mockRl.emit('close');
    
    await expect(inputPromise).rejects.toThrow('Readline interface was closed unexpectedly');
    expect(mockRl.close).toHaveBeenCalled();
    expect(mockRl.removeAllListeners).toHaveBeenCalled();
  });

  test('should handle stdin end event', async () => {
    const inputPromise = getUserInput('TestProject', 'Enter something');
    
    // Simulate stdin end
    mockStdin.emit('end');
    
    await expect(inputPromise).rejects.toThrow('stdin stream ended unexpectedly');
    expect(mockRl.close).toHaveBeenCalled();
    expect(mockRl.removeAllListeners).toHaveBeenCalled();
  });

  test('should prevent race conditions between timeout and user input', async () => {
    const inputPromise = getUserInput('TestProject', 'Enter something');
    
    // Advance time almost to timeout
    jest.advanceTimersByTime(59000);
    
    // Simulate user input just before timeout
    const questionCallback = mockRl.question.mock.calls[0][1];
    questionCallback('quick response');
    
    const result = await inputPromise;
    
    expect(result.response).toBe('quick response');
    expect(mockRl.close).toHaveBeenCalled();
    expect(mockRl.removeAllListeners).toHaveBeenCalled();
  });

  test('should prevent race conditions between multiple error sources', async () => {
    const inputPromise = getUserInput('TestProject', 'Enter something');
    
    // Simulate multiple simultaneous errors
    const readlineError = new Error('Readline error');
    const stdinError = new Error('Stdin error');
    
    mockRl.emit('error', readlineError);
    mockStdin.emit('error', stdinError);
    
    // Should only reject once with the first error
    await expect(inputPromise).rejects.toThrow('Readline interface error: Readline error');
    expect(mockRl.close).toHaveBeenCalledTimes(1);
    expect(mockRl.removeAllListeners).toHaveBeenCalledTimes(1);
  });

  test('should handle errors in question callback', async () => {
    const inputPromise = getUserInput('TestProject', 'Enter something');

    // Mock Date.now to throw an error
    const originalDateNow = Date.now;
    const mockDateNow = jest.fn().mockImplementation(() => {
      throw new Error('Date.now error');
    });
    (Date as any).now = mockDateNow;

    try {
      const questionCallback = mockRl.question.mock.calls[0][1];
      questionCallback('test response');

      await expect(inputPromise).rejects.toThrow('Date.now error');
      expect(mockRl.close).toHaveBeenCalled();
      expect(mockRl.removeAllListeners).toHaveBeenCalled();
    } finally {
      (Date as any).now = originalDateNow;
    }
  });

  test('should handle cleanup errors gracefully', async () => {
    // Mock readline.close to throw an error
    mockRl.close.mockImplementation(() => {
      throw new Error('Close error');
    });
    
    const inputPromise = getUserInput('TestProject', 'Enter something');
    
    // Simulate user input
    const questionCallback = mockRl.question.mock.calls[0][1];
    questionCallback('test response');
    
    const result = await inputPromise;
    
    expect(result.response).toBe('test response');
    expect(consoleWarnSpy).toHaveBeenCalledWith(
      'Warning: Error closing readline interface:',
      expect.any(Error)
    );
  });

  test('should reject when stdin is not available', async () => {
    // Mock process.stdin as null
    Object.defineProperty(process, 'stdin', {
      value: null,
      writable: true,
      configurable: true
    });
    
    await expect(getUserInput('TestProject', 'Enter something'))
      .rejects.toThrow('process.stdin is not available or not readable');
  });

  test('should reject when stdin is not readable', async () => {
    // Mock process.stdin as not readable
    Object.defineProperty(process, 'stdin', {
      value: { readable: false },
      writable: true,
      configurable: true
    });
    
    await expect(getUserInput('TestProject', 'Enter something'))
      .rejects.toThrow('process.stdin is not available or not readable');
  });

  test('should format prompt correctly with predefined options', async () => {
    const options = ['Option 1', 'Option 2', 'Option 3'];
    const inputPromise = getUserInput('TestProject', 'Choose an option', options);
    
    const questionCall = mockRl.question.mock.calls[0];
    const prompt = questionCall[0];
    
    expect(prompt).toContain('[TestProject] Choose an option');
    expect(prompt).toContain('Options:');
    expect(prompt).toContain('1. Option 1');
    expect(prompt).toContain('2. Option 2');
    expect(prompt).toContain('3. Option 3');
    expect(prompt).toContain('Your response:');
    
    // Clean up
    const questionCallback = questionCall[1];
    questionCallback('1');
    await inputPromise;
  });
});
