{"name": "make-dir", "version": "4.0.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": "sindresorhus/make-dir", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && nyc ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["mkdir", "mkdirp", "make", "directories", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"semver": "^7.5.3"}, "devDependencies": {"@types/graceful-fs": "^4.1.3", "@types/node": "^14.14.6", "ava": "^2.4.0", "codecov": "^3.2.0", "graceful-fs": "^4.1.15", "nyc": "^15.0.0", "path-type": "^4.0.0", "tempy": "^1.0.0", "tsd": "^0.13.1", "xo": "^0.34.2"}, "nyc": {"reporter": ["text", "lcov"]}}