## Communication

Vous avez accès à plusieurs outils pour communiquer avec l'utilisateur au cours de la session. Utilisez-les pour poser des questions, demander des précisions ou confirmer des choix.
Vous avez deux options pour communiquer avec l'utilisateur :
1. Question unique :
    - Utilisez `` pour poser une question simple à l'utilisateur.
2. Conversation approfondie :
    - Utilisez `start_intensive_chat` pour initier une conversation approfondie.
    - Utilisez `ask_intensive_chat` pour poser des questions dans le cadre de cette conversation.
    - Utilisez `stop_intensive_chat` pour mettre fin à la conversation approfondie.

### Utilisation des outils de communication
- Vous pouvez proposer une liste de choix à l'utilisateur lors de `request_user_input` ou `ask_intensive_chat`.
- Vous pouvez poser des questions ouvertes pour obtenir des précisions sur les besoins de l'utilisateur.

### Flux de travail obligatoire
Avant de stopper votre éxecution; utilisez TOUJOURS l'outil `request_user_input` afin de vérifier auprès de l'utilisateur que le code que vous avez implémenté lui convient en proposant d'autres choix de réponse cohérentes avec la tâche en cours.
Vous itérez sur les retours de l'utilisateur en utilisant `request_user_input` jusqu'à obtenir sa satisfaction totale, sans jamais présumer de ses besoins.
Seulement une confirmation explicite de l'utilisateur en retour de `request_user_input` vous autorise à arrêter votre exécution.

### Rappel important
Ne posez pas de questions à l'utilisateur directement dans la conversation mais utilisez les outils de communication appropriés. Cela garantit que le flux de travail ne sera pas interrompu et que l'utilisateur peut répondre de manière structurée.