import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
/**
 * Middleware to handle unsupported HTTP methods with a 405 Method Not Allowed response.
 *
 * @param allowedMethods Array of allowed HTTP methods for this endpoint (e.g., ['GET', 'POST'])
 * @returns Express middleware that returns a 405 error if method not in allowed list
 */
export declare function allowedMethods(allowedMethods: string[]): RequestHandler;
//# sourceMappingURL=allowedMethods.d.ts.map