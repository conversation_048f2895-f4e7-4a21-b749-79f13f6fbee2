# Enhanced Interactive Agent Features

Ce document décrit les nouvelles fonctionnalités ajoutées au système `interactive-agent` pour améliorer la gestion des ressources et la stabilité.

## 🎯 Objectifs

- **Gestion robuste des fichiers temporaires** avec nettoyage automatique
- **Système de retry intelligent** avec backoff exponentiel
- **Suivi des processus orphelins** avec nettoyage automatique
- **Gestion avancée des timeouts** adaptatifs
- **Fallbacks intelligents** pour différents environnements

## 🏗️ Architecture

### Gestionnaire de Fichiers Temporaires (`temp-file-manager.ts`)

```typescript
import { tempFileManager } from './utils/temp-file-manager.js';

// Créer un fichier temporaire
const tempFile = await tempFileManager.createTempFile('prefix', '.txt', 'contenu');

// Créer un répertoire temporaire
const tempDir = await tempFileManager.createTempDir('prefix');

// Nettoyage automatique via FinalizationRegistry
// Nettoyage manuel
await tempFile.dispose();
```

**Fonctionnalités :**
- Nettoyage automatique via `FinalizationRegistry`
- Gestion des signaux de processus (SIGINT, SIGTERM)
- IDs de session uniques pour éviter les collisions
- Gestion gracieuse des erreurs de nettoyage

### Gestionnaire de Retry (`retry-manager.ts`)

```typescript
import { RetryManager, RETRY_POLICIES } from './utils/retry-manager.js';

// Exécuter avec retry
const result = await RetryManager.executeWithRetry(
  async () => {
    // Opération qui peut échouer
    return await riskyOperation();
  },
  RETRY_POLICIES.SPAWN_RETRY_POLICY
);

if (result.success) {
  console.log('Succès:', result.result);
} else {
  console.log('Échec après', result.attempts, 'tentatives');
}
```

**Politiques pré-configurées :**
- `SPAWN_RETRY_POLICY` - Pour le spawn de processus
- `FILE_OPERATION_RETRY_POLICY` - Pour les opérations fichier
- `NETWORK_RETRY_POLICY` - Pour les opérations réseau

### Gestionnaire de Processus Orphelins (`orphan-manager.ts`)

```typescript
import { orphanManager } from './utils/orphan-manager.js';

// Enregistrement automatique lors du spawn
const process = spawn('command', args);
orphanManager.registerProcess(process, 'session-id');

// Surveillance des heartbeats
orphanManager.updateHeartbeat(process.pid);

// Nettoyage des processus stagnants
await orphanManager.killStaleProcesses(30000); // 30 secondes
```

**Fonctionnalités :**
- Enregistrement automatique des processus
- Surveillance des heartbeats
- Nettoyage gracieux (SIGTERM puis SIGKILL)
- Support multi-plateforme (Windows/POSIX)

### Fallbacks de Terminal (`terminal-fallbacks.ts`)

```typescript
import { TerminalFallbacks } from './platform/terminal-fallbacks.js';

// Détecter l'environnement
const env = TerminalFallbacks.detectEnvironment();
console.log('SSH:', env.isSSH, 'Docker:', env.isDocker);

// Trouver le meilleur terminal
const terminal = await TerminalFallbacks.findBestTerminal();

// Stratégie de fallback
const strategy = TerminalFallbacks.getFallbackStrategy(); // 'terminal' | 'console' | 'none'
```

**Détection d'environnement :**
- Sessions SSH
- Conteneurs Docker
- Environnements CI/CD
- Environnements headless

## 🚀 Utilisation

### Spawn Amélioré avec Retry

```typescript
import { spawnWithRetry } from './platform/process-spawn.js';

const result = await spawnWithRetry('terminal-command', ['args'], {
  sessionId: 'my-session',
  retryPolicy: {
    maxRetries: 3,
    baseDelayMs: 1000,
    maxDelayMs: 10000,
    backoffFactor: 2.0,
    retryableErrors: ['ENOENT', 'EACCES']
  },
  spawnTimeout: 5000,
  abortSignal: controller.signal
});
```

### Input Utilisateur Amélioré

```typescript
import { getUserInput } from './utils/input.js';

const response = await getUserInput(
  'mon-projet',
  'Votre message',
  ['Option 1', 'Option 2'],
  true, // useTerminalIfAvailable
  retryPolicy,
  abortSignal
);

console.log('Réponse:', response.response);
console.log('Utilisé terminal:', response.usedTerminal);
console.log('Tentatives:', response.retryCount);
```

## 🔧 Configuration

### Constantes de Timeout

```typescript
// Dans constants.ts
export const SPAWN_START_TIMEOUT_MS = 5000;
export const HEARTBEAT_TIMEOUT_MS = 10000;
export const HEARTBEAT_INTERVAL_MS = 2000;
export const PROCESS_KILL_TIMEOUT_MS = 3000;
```

### Politiques de Retry Personnalisées

```typescript
const customPolicy = {
  maxRetries: 5,
  baseDelayMs: 500,
  maxDelayMs: 15000,
  backoffFactor: 1.5,
  retryableErrors: ['CUSTOM_ERROR']
};
```

## 🧪 Tests

Exécuter les tests des nouvelles fonctionnalités :

```bash
node test-enhanced-features.js
```

Les tests couvrent :
- Gestion des fichiers temporaires
- Logique de retry
- Suivi des processus orphelins
- Détection des fallbacks de terminal
- Spawn amélioré
- Input console de base

## 🔒 Sécurité et Robustesse

### Nettoyage Automatique
- Fichiers temporaires nettoyés automatiquement
- Processus orphelins tués lors de l'arrêt
- Gestion des signaux système

### Gestion d'Erreurs
- Retry intelligent avec backoff exponentiel
- Fallbacks gracieux vers la console
- Timeouts adaptatifs par opération

### Compatibilité
- Support Windows, macOS, Linux
- Détection automatique d'environnement
- Fallbacks pour environnements contraints

## 📊 Monitoring

### Logs Structurés
```typescript
console.debug('Process registered', { pid, sessionId });
console.warn('Stale process detected', { pid, lastHeartbeat });
```

### Métriques
- Nombre de tentatives de retry
- Temps total de delay
- Processus suivis
- Fichiers temporaires actifs

## 🔄 Rétrocompatibilité

Toutes les fonctions existantes continuent de fonctionner :
- `getUserInput()` sans paramètres supplémentaires
- `spawnInTerminal()` inchangé
- API existante préservée

Les nouvelles fonctionnalités sont opt-in via des paramètres optionnels.
