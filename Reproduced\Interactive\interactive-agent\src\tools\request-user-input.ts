/**
 * Simplified request-user-input tool
 * Streamlined version of interactive-mcp tool with essential functionality only
 */

import { z } from 'zod';
import {
  CapabilityInfo,
  ToolResultUtils,
  ErrorAwareToolHandler,
  ToolDefinition
} from './types.js';
import { getUserInput } from '../utils/input.js';

// Interface for validated request user input arguments
export interface RequestUserInputArgs {
  projectName: string;
  message: string;
  predefinedOptions?: string[];
}

// Zod schema for request user input arguments
const RequestUserInputSchema = z.object({
  projectName: z.string().min(1, 'Project name is required'),
  message: z.string().min(1, 'Message is required'),
  predefinedOptions: z.array(z.string()).optional()
}) satisfies z.ZodSchema<RequestUserInputArgs>;

// Enhanced tool handler implementation with better type safety
const handleRequestUserInput: ErrorAwareToolHandler<RequestUserInputArgs> = async (args) => {
  // Parse and validate the arguments using our schema
  const validatedArgs = RequestUserInputSchema.parse(args);
  const { projectName, message, predefinedOptions } = validatedArgs;

  try {
    const result = await getUserInput(
      projectName,
      message,
      predefinedOptions
    );

    return ToolResultUtils.success(`User response: ${result.response}`);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    return ToolResultUtils.error(`Error getting user input: ${errorMessage}`);
  }
};

// Simplified capability info
export const requestUserInputCapability: CapabilityInfo = {
  name: 'request_user_input',
  description: 'Request input from user with optional predefined choices'
};

// Enhanced tool definition with better type safety
export const requestUserInputTool: ToolDefinition<RequestUserInputArgs> = {
  name: 'request_user_input',
  description: 'Request input from the user during tool execution',
  inputSchema: RequestUserInputSchema,
  handler: handleRequestUserInput
};
