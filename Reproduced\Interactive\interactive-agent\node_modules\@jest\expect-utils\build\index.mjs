import cjsModule from './index.js';

export const equals = cjsModule.equals;
export const isA = cjsModule.isA;
export const arrayBufferEquality = cjsModule.arrayBufferEquality;
export const emptyObject = cjsModule.emptyObject;
export const getObjectKeys = cjsModule.getObjectKeys;
export const getObjectSubset = cjsModule.getObjectSubset;
export const getPath = cjsModule.getPath;
export const isError = cjsModule.isError;
export const isOneline = cjsModule.isOneline;
export const iterableEquality = cjsModule.iterableEquality;
export const partition = cjsModule.partition;
export const pathAsArray = cjsModule.pathAsArray;
export const sparseArrayEquality = cjsModule.sparseArrayEquality;
export const subsetEquality = cjsModule.subsetEquality;
export const typeEquality = cjsModule.typeEquality;
