export interface TerminalInfo {
    name: string;
    command: string;
    args: string[];
    available: boolean;
    priority: number;
}
export interface FallbackStrategy {
    useConsole: boolean;
    preferredTerminal: TerminalInfo | null;
    fallbackTerminals: TerminalInfo[];
}
/**
 * Teste si une commande est disponible sur le système
 */
export declare function isCommandAvailable(command: string): Promise<boolean>;
/**
 * Détecte les terminaux disponibles pour la plateforme actuelle
 */
export declare function detectAvailableTerminals(): Promise<TerminalInfo[]>;
/**
 * Détermine la stratégie de fallback appropriée
 */
export declare function getFallbackStrategy(): Promise<FallbackStrategy>;
/**
 * Vérifie si l'environnement supporte les terminaux GUI
 */
export declare function supportsGUITerminals(): boolean;
/**
 * Obtient le terminal par défaut pour la plateforme
 */
export declare function getDefaultTerminal(): TerminalInfo | null;
/**
 * Teste si un terminal spécifique peut être lancé
 */
export declare function testTerminalLaunch(terminal: TerminalInfo): Promise<boolean>;
//# sourceMappingURL=terminal-fallbacks.d.ts.map