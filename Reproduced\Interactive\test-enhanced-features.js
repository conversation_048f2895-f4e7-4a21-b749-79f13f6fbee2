#!/usr/bin/env node

/**
 * Test script for enhanced interactive-agent features
 * Tests the new temporary file management, retry logic, and orphan process tracking
 */

import { tempFileManager } from './interactive-agent/src/utils/temp-file-manager.js';
import { RetryManager, RETRY_POLICIES } from './interactive-agent/src/utils/retry-manager.js';
import { orphanManager } from './interactive-agent/src/utils/orphan-manager.js';
import { TerminalFallbacks } from './interactive-agent/src/platform/terminal-fallbacks.js';
import { spawnWithRetry } from './interactive-agent/src/platform/process-spawn.js';
import { getUserInput } from './interactive-agent/src/utils/input.js';

async function testTempFileManager() {
  console.log('\n=== Testing Temporary File Manager ===');
  
  try {
    // Test creating temporary files
    const tempFile1 = await tempFileManager.createTempFile('test', '.txt', 'Hello World');
    console.log('✓ Created temp file:', tempFile1.path);
    
    const tempDir = await tempFileManager.createTempDir('test-dir');
    console.log('✓ Created temp directory:', tempDir.path);
    
    // Test disposal
    await tempFile1.dispose();
    console.log('✓ Disposed temp file');
    
    await tempDir.dispose();
    console.log('✓ Disposed temp directory');
    
    console.log('✓ Temporary file manager tests passed');
  } catch (error) {
    console.error('✗ Temporary file manager test failed:', error.message);
  }
}

async function testRetryManager() {
  console.log('\n=== Testing Retry Manager ===');
  
  try {
    let attempts = 0;
    
    // Test successful retry
    const result = await RetryManager.executeWithRetry(
      async () => {
        attempts++;
        if (attempts < 3) {
          throw new Error('Simulated failure');
        }
        return 'Success!';
      },
      {
        maxRetries: 3,
        baseDelayMs: 100,
        maxDelayMs: 1000,
        backoffFactor: 2,
        retryableErrors: ['Simulated failure']
      }
    );
    
    if (result.success && result.result === 'Success!' && result.attempts === 3) {
      console.log('✓ Retry logic works correctly');
    } else {
      console.error('✗ Retry logic failed:', result);
    }
    
    console.log('✓ Retry manager tests passed');
  } catch (error) {
    console.error('✗ Retry manager test failed:', error.message);
  }
}

async function testOrphanManager() {
  console.log('\n=== Testing Orphan Manager ===');
  
  try {
    // Test process tracking
    const processCount = orphanManager.getProcessCount();
    console.log('✓ Current tracked processes:', processCount);
    
    // Test environment detection
    const environment = TerminalFallbacks.detectEnvironment();
    console.log('✓ Environment detection:', {
      isSSH: environment.isSSH,
      isDocker: environment.isDocker,
      isCI: environment.isCI,
      isHeadless: environment.isHeadless,
      hasDisplay: environment.hasDisplay
    });
    
    console.log('✓ Orphan manager tests passed');
  } catch (error) {
    console.error('✗ Orphan manager test failed:', error.message);
  }
}

async function testTerminalFallbacks() {
  console.log('\n=== Testing Terminal Fallbacks ===');
  
  try {
    // Test environment detection
    const environment = TerminalFallbacks.detectEnvironment();
    console.log('✓ Environment detected:', environment);
    
    // Test fallback strategy
    const strategy = TerminalFallbacks.getFallbackStrategy();
    console.log('✓ Fallback strategy:', strategy);
    
    // Test terminal availability (this might take a moment)
    console.log('Testing terminal availability...');
    const canSpawn = await TerminalFallbacks.canSpawnTerminal();
    console.log('✓ Can spawn terminal:', canSpawn);
    
    console.log('✓ Terminal fallbacks tests passed');
  } catch (error) {
    console.error('✗ Terminal fallbacks test failed:', error.message);
  }
}

async function testEnhancedSpawn() {
  console.log('\n=== Testing Enhanced Spawn ===');
  
  try {
    // Test spawn with retry (using a command that should exist)
    const result = await spawnWithRetry('echo', ['test'], {
      sessionId: 'test-session',
      spawnTimeout: 5000
    });
    
    if (result.success) {
      console.log('✓ Enhanced spawn successful');
      
      // Clean up the process
      if (result.process && result.process.pid) {
        await orphanManager.killProcess(result.process.pid);
        console.log('✓ Process cleaned up');
      }
    } else {
      console.log('ℹ Enhanced spawn failed (expected in some environments):', result.error);
    }
    
    console.log('✓ Enhanced spawn tests completed');
  } catch (error) {
    console.error('✗ Enhanced spawn test failed:', error.message);
  }
}

async function testConsoleInput() {
  console.log('\n=== Testing Console Input (Basic) ===');
  
  try {
    // Test basic console input with timeout
    console.log('Testing console input (will timeout in 5 seconds)...');
    
    const timeoutController = new AbortController();
    setTimeout(() => timeoutController.abort(), 5000);
    
    try {
      const response = await getUserInput(
        'test-project',
        'This is a test message (will timeout)',
        ['Option 1', 'Option 2'],
        false, // Don't use terminal
        undefined,
        timeoutController.signal
      );
      console.log('✓ Got response:', response);
    } catch (error) {
      if (error.message.includes('timeout') || error.message.includes('aborted')) {
        console.log('✓ Timeout/abort handling works correctly');
      } else {
        throw error;
      }
    }
    
    console.log('✓ Console input tests passed');
  } catch (error) {
    console.error('✗ Console input test failed:', error.message);
  }
}

async function runAllTests() {
  console.log('🚀 Starting Enhanced Interactive Agent Tests');
  console.log('='.repeat(50));
  
  await testTempFileManager();
  await testRetryManager();
  await testOrphanManager();
  await testTerminalFallbacks();
  await testEnhancedSpawn();
  await testConsoleInput();
  
  console.log('\n' + '='.repeat(50));
  console.log('✅ All tests completed!');
  console.log('\nNote: Some tests may show expected failures in certain environments');
  console.log('(e.g., headless environments, CI systems, etc.)');
  
  // Final cleanup
  await tempFileManager.disposeAll();
  console.log('🧹 Cleanup completed');
}

// Run tests
runAllTests().catch(error => {
  console.error('❌ Test suite failed:', error);
  process.exit(1);
});
