export { PlatformInfo, TerminalCapabilities, getPlatformInfo, getPlatformInfoAsync, refreshPlatformCache, refreshPlatformCacheAsync, getTerminalCapabilities, getTerminalCapabilitiesAsync } from './platform-detector.js';
export { SpawnOptions, SpawnResult, spawnInTerminal, testTerminalSpawn } from './process-spawn.js';
export { TerminalInfo, FallbackStrategy, detectAvailableTerminals, getFallbackStrategy, supportsGUITerminals, getDefaultTerminal, testTerminalLaunch } from './terminal-fallbacks.js';
import { SpawnResult } from './process-spawn.js';
import { PlatformType } from '../constants.js';
/**
 * Fonction de convenance pour vérifier si le spawn de terminal est possible
 */
export declare function canSpawnTerminal(): boolean;
/**
 * Fonction de convenance pour obtenir les terminaux disponibles (avec vérification réelle)
 */
export declare function getAvailableTerminals(): Promise<string[]>;
/**
 * Fonction de convenance pour obtenir les terminaux disponibles depuis platform-detector
 */
export declare function getAvailableTerminalsFromPlatform(): Promise<string[]>;
/**
 * Fonction de convenance pour détecter le shell par défaut
 */
export declare function detectShell(): string;
/**
 * Fonction de convenance pour spawn avec fallback automatique
 */
export declare function spawnWithFallback(command: string, args?: string[], options?: import('./process-spawn.js').SpawnOptions): Promise<SpawnResult & {
    usedFallback: boolean;
}>;
/**
 * Obtient le type de plateforme simplifié
 */
export declare function getPlatformType(): PlatformType;
/**
 * Vérifie si la plateforme est Windows (incluant WSL)
 */
export declare function isWindows(): boolean;
/**
 * Vérifie si la plateforme est macOS
 */
export declare function isMacOS(): boolean;
/**
 * Vérifie si la plateforme est Linux (excluant WSL)
 */
export declare function isLinux(): boolean;
/**
 * Vérifie si nous sommes dans WSL
 */
export declare function isWSL(): boolean;
/**
 * Obtient un résumé des capacités de la plateforme
 */
export interface PlatformSummary {
    type: PlatformType;
    canSpawnTerminal: boolean;
    hasDisplay: boolean;
    isHeadless: boolean;
    preferredShell: string;
    terminalCount: number;
}
export declare function getPlatformSummary(): PlatformSummary;
//# sourceMappingURL=index.d.ts.map