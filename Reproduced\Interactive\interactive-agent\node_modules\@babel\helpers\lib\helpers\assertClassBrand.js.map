{"version": 3, "names": ["_assert<PERSON>lassBrand", "brand", "receiver", "returnValue", "has", "arguments", "length", "TypeError"], "sources": ["../../src/helpers/assertClassBrand.ts"], "sourcesContent": ["/* @minVersion 7.24.0 */\n\nexport default function _assertClassBrand(\n  brand: Function | WeakMap<any, any> | WeakSet<any>,\n  receiver: any,\n  returnValue?: any,\n) {\n  if (typeof brand === \"function\" ? brand === receiver : brand.has(receiver)) {\n    return arguments.length < 3 ? receiver : returnValue;\n  }\n  throw new TypeError(\"Private element is not present on this object\");\n}\n"], "mappings": ";;;;;;AAEe,SAASA,iBAAiBA,CACvCC,KAAkD,EAClDC,QAAa,EACbC,WAAiB,EACjB;EACA,IAAI,OAAOF,KAAK,KAAK,UAAU,GAAGA,KAAK,KAAKC,QAAQ,GAAGD,KAAK,CAACG,GAAG,CAACF,QAAQ,CAAC,EAAE;IAC1E,OAAOG,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGJ,QAAQ,GAAGC,WAAW;EACtD;EACA,MAAM,IAAII,SAAS,CAAC,+CAA+C,CAAC;AACtE", "ignoreList": []}