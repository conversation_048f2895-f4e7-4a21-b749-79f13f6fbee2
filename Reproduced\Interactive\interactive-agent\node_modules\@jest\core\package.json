{"name": "@jest/core", "description": "Delightful JavaScript Testing.", "version": "30.0.5", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/console": "30.0.5", "@jest/pattern": "30.0.1", "@jest/reporters": "30.0.5", "@jest/test-result": "30.0.5", "@jest/transform": "30.0.5", "@jest/types": "30.0.5", "@types/node": "*", "ansi-escapes": "^4.3.2", "chalk": "^4.1.2", "ci-info": "^4.2.0", "exit-x": "^0.2.2", "graceful-fs": "^4.2.11", "jest-changed-files": "30.0.5", "jest-config": "30.0.5", "jest-haste-map": "30.0.5", "jest-message-util": "30.0.5", "jest-regex-util": "30.0.1", "jest-resolve": "30.0.5", "jest-resolve-dependencies": "30.0.5", "jest-runner": "30.0.5", "jest-runtime": "30.0.5", "jest-snapshot": "30.0.5", "jest-util": "30.0.5", "jest-validate": "30.0.5", "jest-watcher": "30.0.5", "micromatch": "^4.0.8", "pretty-format": "30.0.5", "slash": "^3.0.0"}, "devDependencies": {"@jest/test-sequencer": "30.0.5", "@jest/test-utils": "30.0.5", "@types/graceful-fs": "^4.1.9", "@types/micromatch": "^4.0.9"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-core"}, "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "homepage": "https://jestjs.io/", "license": "MIT", "keywords": ["ava", "babel", "coverage", "easy", "expect", "facebook", "immersive", "instant", "jasmine", "jest", "jsdom", "mocha", "mocking", "painless", "qunit", "runner", "sandboxed", "snapshot", "tap", "tape", "test", "testing", "typescript", "watch"], "publishConfig": {"access": "public"}, "gitHead": "22236cf58b66039f81893537c90dee290bab427f"}