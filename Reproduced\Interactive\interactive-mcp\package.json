{"name": "interactive-mcp", "version": "1.0.0", "main": "dist/index.js", "type": "module", "bin": {"interactive-mcp": "dist/index.js"}, "files": ["dist", "README.md", "LICENSE", "package.json"], "scripts": {"build": "tsc --outDir dist && tsc-alias", "start": "node dist/index.js", "lint": "eslint \"src/**/*.{js,ts,jsx,tsx}\"", "format": "prettier --write \"src/**/*.{js,ts,jsx,tsx,json,md}\"", "check-types": "tsc --noEmit", "prepare": "husky"}, "keywords": [], "author": "", "license": "MIT", "description": "", "devDependencies": {"@eslint/js": "^9.25.1", "@semantic-release/commit-analyzer": "^13.0.1", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^11.0.2", "@semantic-release/npm": "^12.0.1", "@semantic-release/release-notes-generator": "^14.0.3", "@types/node": "^22.15.2", "@types/node-notifier": "^8.0.5", "@types/pino": "^7.0.5", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "conventional-changelog-conventionalcommits": "^8.0.0", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "globals": "^16.0.0", "husky": "^9.1.7", "jiti": "^2.4.2", "lint-staged": "^15.5.1", "pino-pretty": "^13.0.0", "prettier": "^3.5.3", "semantic-release": "^24.2.3", "tsc-alias": "^1.8.15", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0"}, "dependencies": {"@inkjs/ui": "^2.0.0", "@modelcontextprotocol/sdk": "^1.10.2", "@types/yargs": "^17.0.33", "ink": "^5.2.0", "node-notifier": "^10.0.1", "pino": "^9.6.0", "react": "^18.3.1", "react-dom": "^18.3.1", "yargs": "^17.7.2", "zod": "^3.24.3"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --fix"], "*.{js,ts,jsx,tsx,json,md}": ["prettier --write"]}}