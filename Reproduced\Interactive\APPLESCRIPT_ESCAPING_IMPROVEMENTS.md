# Améliorations de l'échappement AppleScript

## Problème identifié

L'implémentation précédente de `createMacOSSpawnOptions()` dans `interactive-agent/src/platform/process-spawn.ts` utilisait un échappement AppleScript très basique qui ne gérait que les guillemets doubles :

```typescript
// Ancien code - échappement insuffisant
const appleScript = `
  tell application "Terminal"
    activate
    do script "${fullCommand.replace(/"/g, '\\"')}"
  end tell
`;
```

Cette approche était vulnérable aux caractères spéciaux AppleScript et pouvait causer des erreurs d'exécution ou des problèmes de sécurité.

## Solution implémentée

### 1. Fonction d'échappement complète

Création d'une fonction `escapeAppleScript()` qui gère tous les caractères spéciaux AppleScript :

```typescript
function escapeAppleScript(str: string): string {
  return str
    // Échapper les backslashes en premier (important pour éviter les doubles échappements)
    .replace(/\\/g, '\\\\')
    // Échapper les guillemets doubles
    .replace(/"/g, '\\"')
    // Échapper les retours à la ligne
    .replace(/\n/g, '\\n')
    .replace(/\r/g, '\\r')
    // Échapper les tabulations
    .replace(/\t/g, '\\t')
    // Échapper les caractères de contrôle AppleScript et shell
    .replace(/\$/g, '\\$')
    .replace(/`/g, '\\`')
    // Échapper les accolades seulement dans le contexte des variables ${...}
    .replace(/\$\{/g, '\\$\\{')
    .replace(/\}/g, '\\}');
}
```

### 2. Approche avec fichier temporaire (plus sûre)

Implémentation d'une approche alternative utilisant un fichier de script temporaire pour éviter complètement les problèmes d'échappement complexes :

```typescript
function createMacOSSpawnOptions(command: string, args: string[] = [], options: SpawnOptions = {}) {
  // Approche sécurisée : utiliser un fichier de script temporaire
  const fs = require('fs');
  const path = require('path');
  const os = require('os');
  
  try {
    // Créer un fichier de script temporaire
    const tempDir = os.tmpdir();
    const scriptPath = path.join(tempDir, `terminal_script_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.sh`);
    
    // Écrire la commande dans le fichier temporaire
    fs.writeFileSync(scriptPath, `#!/bin/bash\n${fullCommand}\n`, { mode: 0o755 });
    
    // Utiliser osascript pour exécuter le script temporaire
    const appleScript = `
      tell application "Terminal"
        activate
        do script "bash '${scriptPath}'; rm '${scriptPath}'"
      end tell
    `;
    
    return { cmd: 'osascript', args: ['-e', appleScript], spawnOptions: {...} };
  } catch (error) {
    // Fallback vers l'approche directe avec échappement amélioré
    const escapedFullCommand = escapeAppleScript(fullCommand);
    // ...
  }
}
```

## Caractères spéciaux gérés

La nouvelle fonction d'échappement gère les caractères suivants :

- **Backslashes (`\`)** : Échappés en premier pour éviter les doubles échappements
- **Guillemets doubles (`"`)** : Échappés pour éviter la fermeture prématurée des chaînes
- **Retours à la ligne (`\n`, `\r`)** : Échappés pour maintenir la structure du script
- **Tabulations (`\t`)** : Échappées pour préserver le formatage
- **Variables shell (`$`, `${...}`)** : Échappées pour éviter l'expansion non désirée
- **Backticks (`` ` ``)** : Échappés pour éviter l'exécution de commandes
- **Accolades (`{}`)** : Échappées dans le contexte des variables

## Fichiers modifiés

1. **`interactive-agent/src/platform/process-spawn.ts`**
   - Ajout de la fonction `escapeAppleScript()`
   - Modification de `createMacOSSpawnOptions()` avec approche fichier temporaire + fallback

2. **`interactive-mcp/src/commands/input/index.ts`**
   - Ajout de la fonction `escapeAppleScript()`
   - Remplacement de l'échappement basique par la nouvelle fonction

3. **`interactive-mcp/src/commands/intensive-chat/index.ts`**
   - Ajout de la fonction `escapeAppleScript()`
   - Remplacement de l'échappement basique par la nouvelle fonction

## Avantages de la solution

### Sécurité
- Gestion complète des caractères spéciaux AppleScript
- Prévention de l'injection de code malveillant
- Échappement correct des variables shell

### Robustesse
- Approche fichier temporaire comme méthode principale (plus sûre)
- Fallback vers échappement direct en cas d'erreur
- Gestion des cas d'erreur avec logging approprié

### Maintenabilité
- Fonction d'échappement réutilisable
- Code documenté et testé
- Séparation claire des responsabilités

## Tests

Un fichier de test `test-applescript-escaping.js` a été créé pour valider le bon fonctionnement de la fonction d'échappement avec différents cas d'usage :

- Commandes simples
- Chemins avec espaces
- Commandes avec backslashes
- Variables shell (`$VAR`, `${VAR}`)
- Backticks et commandes imbriquées
- Caractères de contrôle (retours à la ligne, tabulations)
- Commandes complexes avec plusieurs caractères spéciaux

## Recommandations futures

1. **Centralisation** : Considérer la création d'un module utilitaire partagé pour les fonctions d'échappement
2. **Tests automatisés** : Intégrer les tests d'échappement dans la suite de tests du projet
3. **Monitoring** : Ajouter des logs pour surveiller l'utilisation des différentes approches (fichier temporaire vs échappement direct)
4. **Configuration** : Permettre de configurer la méthode d'échappement préférée via les options
