I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The current `interactive-agent` project has basic platform detection and process spawning capabilities, but lacks robust temporary file management, intelligent error handling, retry mechanisms, and orphan process cleanup. The user wants to enhance these areas to make the system more reliable in adverse environments while maintaining the minimal architecture approach.

### Approach

I'll implement a comprehensive hardening system that adds:
- **Temporary File Manager**: Auto-cleanup system with `FinalizationRegistry` for garbage collection safety
- **Enhanced Timeout System**: Adaptive timeouts for different operations (spawn, heartbeat, user input)
- **Intelligent Retry Logic**: Exponential backoff with configurable retry policies for different error types
- **Orphan Process Tracking**: Registry-based tracking with cleanup on parent exit and signal handling

The approach focuses on reliability without over-engineering, using Node.js built-ins and maintaining backward compatibility.

### Reasoning

I explored the existing `interactive-agent` codebase to understand the current implementation, including platform detection (`platform-detector.ts`), process spawning (`process-spawn.ts`), and input handling (`utils/input.ts`). I also examined the original `interactive-mcp` implementation to understand the temporary file patterns and heartbeat monitoring approach that needs to be improved and adapted.

## Mermaid Diagram

sequenceDiagram
    participant App as Application
    participant Input as Enhanced Input
    participant TempMgr as Temp File Manager
    participant Retry as Retry Manager
    participant Spawn as Process Spawn
    participant Orphan as Orphan Manager
    participant FS as File System

    App->>Input: getUserInput(useTerminal=true)
    Input->>TempMgr: createTempFile(response, heartbeat, options)
    TempMgr->>FS: Create temp files with session ID
    TempMgr->>Input: Return file handles with auto-cleanup

    Input->>Retry: executeWithRetry(spawnOperation)
    Retry->>Spawn: spawnWithRetry(terminal, args)
    Spawn->>Orphan: registerProcess(childProcess)
    
    alt Spawn Success
        Spawn->>Input: Return process handle
        Input->>Input: Start heartbeat monitoring
        Input->>Input: Watch response file
        
        loop Heartbeat Check
            Input->>FS: Check heartbeat file timestamp
            alt Stale Process
                Input->>Orphan: killProcess(pid)
                Input->>TempMgr: cleanup files
            end
        end
        
        alt User Response
            FS->>Input: File change event
            Input->>TempMgr: cleanup files
            Input->>App: Return user response
        else Timeout
            Input->>Orphan: killProcess(pid)
            Input->>TempMgr: cleanup files
            Input->>App: Return timeout error
        end
        
    else Spawn Failure
        Retry->>Retry: Apply exponential backoff
        alt Max Retries Reached
            Retry->>Input: Return aggregated error
            Input->>Input: Fallback to console mode
            Input->>App: Return console input
        else Retry Available
            Retry->>Spawn: Retry spawn operation
        end
    end

    Note over Orphan: Background cleanup of stale processes
    Note over TempMgr: FinalizationRegistry ensures cleanup on GC

## Proposed File Changes

### interactive-agent\src\utils\temp-file-manager.ts(NEW)

References: 

- interactive-mcp\src\commands\input\index.ts

Create a comprehensive temporary file management system with automatic cleanup capabilities. Implement:

- **TempFile Interface**: Define structure with `path`, `dispose()`, `isDisposed` properties
- **TempFileManager Class**: Singleton pattern with methods:
  - `createTempFile(prefix, extension?, content?)` - creates temp file with unique session ID
  - `createTempDir(prefix?)` - creates temporary directory
  - `registerForCleanup(path)` - adds existing files to cleanup registry
  - `disposeAll()` - cleanup all managed files/directories
- **Automatic Cleanup**: Use `FinalizationRegistry` to cleanup files when TempFile objects are garbage collected
- **Process Exit Handlers**: Register cleanup on `process.on('exit')`, `process.on('SIGINT')`, `process.on('SIGTERM')`
- **Session Management**: Use crypto.randomBytes for unique session IDs to avoid collisions
- **Error Handling**: Graceful handling of cleanup failures with logging but no throwing
- **Path Utilities**: Helper methods for generating session-based filenames and checking file existence
- **Disposal Tracking**: Prevent double-disposal and track cleanup status

### interactive-agent\src\utils\retry-manager.ts(NEW)

References: 

- interactive-mcp\src\commands\input\index.ts

Create an intelligent retry system for handling spawn failures and other operations. Implement:

- **RetryPolicy Interface**: Define retry configuration with `maxRetries`, `baseDelayMs`, `maxDelayMs`, `backoffFactor`, `retryableErrors`
- **RetryableError Types**: Categorize errors that should trigger retries:
  - Spawn errors: `ENOENT`, `EACCES`, `EMFILE`
  - Process errors: Non-zero exit codes, spawn event failures
  - Timeout errors: Spawn timeout, heartbeat timeout
- **Exponential Backoff**: Implement `calculateDelay(attempt, policy)` with jitter to avoid thundering herd
- **Retry Executor**: Generic `executeWithRetry<T>(operation, policy)` function that:
  - Wraps any async operation
  - Applies retry logic with proper delay
  - Tracks attempt count and errors
  - Returns aggregated error information on final failure
- **Default Policies**: Pre-configured policies for common scenarios:
  - `SPAWN_RETRY_POLICY` - for process spawning
  - `FILE_OPERATION_RETRY_POLICY` - for file I/O
  - `NETWORK_RETRY_POLICY` - for potential future network operations
- **Abort Support**: Integration with `AbortController` for cancellable retries
- **Logging Integration**: Structured logging of retry attempts with context

### interactive-agent\src\utils\orphan-manager.ts(NEW)

References: 

- interactive-mcp\src\commands\input\index.ts

Create a robust orphan process tracking and cleanup system. Implement:

- **OrphanManager Singleton**: Global registry for tracking spawned child processes
- **Process Registry**: `Map<number, ProcessInfo>` where ProcessInfo contains:
  - `pid`: Process ID
  - `process`: ChildProcess reference
  - `spawnTime`: When process was created
  - `lastHeartbeat`: Last activity timestamp
  - `sessionId`: Associated session identifier
- **Registration Methods**:
  - `registerProcess(process, sessionId?)` - add process to tracking
  - `unregisterProcess(pid)` - remove from tracking when process exits normally
- **Health Monitoring**:
  - `checkProcessHealth(pid)` - verify process is still alive using `process.kill(pid, 0)`
  - `updateHeartbeat(pid)` - update last activity timestamp
  - `findStaleProcesses(timeoutMs)` - identify processes that haven't had activity
- **Cleanup Operations**:
  - `killProcess(pid, signal?)` - graceful then forceful termination
  - `killAllProcesses()` - cleanup all tracked processes
  - `killStaleProcesses(timeoutMs)` - cleanup inactive processes
- **Signal Handlers**: Automatic registration of cleanup on parent process exit
- **Platform-Specific Killing**: Handle Windows vs POSIX differences in process termination
- **Graceful Shutdown**: SIGTERM first, then SIGKILL after timeout

### interactive-agent\src\constants.ts(MODIFY)

References: 

- interactive-mcp\src\constants.ts

Extend the constants file with comprehensive timeout and retry configuration. Add new constants while preserving existing `USER_INPUT_TIMEOUT_SECONDS`:

- **Timeout Constants**:
  - `SPAWN_START_TIMEOUT_MS` - how long to wait for child process spawn event (5000ms)
  - `HEARTBEAT_TIMEOUT_MS` - max inactivity before considering process orphaned (10000ms)
  - `HEARTBEAT_INTERVAL_MS` - frequency of heartbeat checks (2000ms)
  - `PROCESS_KILL_TIMEOUT_MS` - time between SIGTERM and SIGKILL (3000ms)
  - `TEMP_FILE_CLEANUP_TIMEOUT_MS` - max time for cleanup operations (1000ms)
- **Retry Configuration**:
  - `DEFAULT_MAX_RETRIES` - default retry attempts (3)
  - `DEFAULT_RETRY_BASE_DELAY_MS` - base delay for exponential backoff (1000ms)
  - `DEFAULT_RETRY_MAX_DELAY_MS` - maximum delay cap (10000ms)
  - `DEFAULT_BACKOFF_FACTOR` - exponential multiplier (2.0)
- **File Management**:
  - `TEMP_FILE_PREFIX` - prefix for temporary files ('interactive-agent')
  - `SESSION_ID_LENGTH` - length of session identifiers (16)
- **Process Management**:
  - `MAX_TRACKED_PROCESSES` - limit on orphan registry size (100)
  - `STALE_PROCESS_CHECK_INTERVAL_MS` - frequency of stale process cleanup (30000ms)
- Use `as const` assertions for all constants to ensure type safety and tree-shaking optimization

### interactive-agent\src\platform\process-spawn.ts(MODIFY)

References: 

- interactive-agent\src\utils\retry-manager.ts(NEW)
- interactive-agent\src\utils\orphan-manager.ts(NEW)
- interactive-agent\src\constants.ts(MODIFY)

Enhance the existing process spawning functionality with retry logic, timeout handling, and orphan tracking. Modifications to existing code:

- **Enhanced SpawnOptions Interface**: Add new optional properties:
  - `retryPolicy?: RetryPolicy` - custom retry configuration
  - `spawnTimeout?: number` - timeout for spawn operation
  - `abortSignal?: AbortSignal` - for cancellation support
  - `sessionId?: string` - for tracking and temp file association
- **Enhanced SpawnResult Interface**: Add tracking and error information:
  - `sessionId?: string` - session identifier for tracking
  - `retryCount?: number` - number of retry attempts made
  - `lastError?: Error` - most recent error encountered
- **New spawnWithRetry Function**: Wrapper around existing `spawnInTerminal` that:
  - Imports and uses `RetryManager.executeWithRetry`
  - Applies spawn timeout using `AbortController`
  - Registers successful spawns with `OrphanManager`
  - Handles platform-specific spawn errors appropriately
- **Timeout Integration**: Add spawn timeout logic:
  - Create `AbortController` with timeout
  - Pass abort signal to spawn options
  - Handle timeout errors in retry logic
- **Orphan Registration**: Automatically register successful spawns:
  - Call `OrphanManager.registerProcess` after successful spawn
  - Include session ID for better tracking
  - Set up process exit handlers to unregister
- **Backward Compatibility**: Keep existing `spawnInTerminal` function unchanged, add new functionality as additional exports

### interactive-agent\src\platform\terminal-fallbacks.ts(MODIFY)

References: 

- interactive-agent\src\platform\platform-detector.ts
- interactive-agent\src\utils\retry-manager.ts(NEW)

Create the missing terminal fallback system that was referenced in previous implementation but not yet created. Implement:

- **Terminal Detection Chain**: Systematic detection with priority ordering:
  - Linux: `gnome-terminal` > `konsole` > `xfce4-terminal` > `mate-terminal` > `xterm` > `urxvt`
  - macOS: `Terminal.app` > `iTerm2` > fallback to `osascript`
  - Windows: `Windows Terminal` > `ConEmu` > `Cmder` > `cmd.exe`
- **Environment-Specific Fallbacks**: Handle special environments:
  - SSH sessions: Check `SSH_CLIENT` and `SSH_TTY` environment variables
  - Docker containers: Detect `/.dockerenv` file
  - CI/CD systems: Check common CI environment variables
  - Headless environments: Automatic fallback to console mode
- **Capability Testing**: Runtime testing of terminal spawn capabilities:
  - `testTerminalAvailability(terminalName)` - test if terminal can be spawned
  - `findWorkingTerminal(terminalList)` - find first working terminal from list
  - Cache test results to avoid repeated expensive operations
- **Graceful Degradation**: Automatic fallback strategies:
  - Terminal spawn failure → console-based input
  - GUI unavailable → text-only mode
  - Permission denied → alternative terminal or console
- **Integration with Retry System**: Use retry logic for terminal capability testing
- **Platform-Specific Helpers**: Utility functions for each platform's terminal detection
- **Configuration Support**: Allow override of terminal preferences via environment variables

### interactive-agent\src\platform\index.ts(MODIFY)

References: 

- interactive-agent\src\platform\platform-detector.ts
- interactive-agent\src\platform\process-spawn.ts(MODIFY)
- interactive-agent\src\platform\terminal-fallbacks.ts(MODIFY)

Create the missing platform module barrel export that provides a clean API for all platform functionality. Export:

- **Main Functions**: Clean interface for platform operations:
  - `getPlatformInfo(forceRefresh?)` - from platform-detector
  - `spawnInTerminal(command, args, options)` - basic spawn from process-spawn
  - `spawnWithRetry(command, args, options)` - enhanced spawn with retry
  - `canSpawnTerminal()` - capability check
  - `findBestTerminal()` - from terminal-fallbacks
- **Type Definitions**: Re-export all interfaces:
  - `PlatformInfo`, `TerminalCapabilities` - from platform-detector
  - `SpawnOptions`, `SpawnResult` - from process-spawn
  - `RetryPolicy` - from retry-manager
- **Utility Functions**: Convenience methods:
  - `refreshPlatformCache()` - force refresh platform detection
  - `getAvailableTerminals()` - list available terminals
  - `detectShell()` - get preferred shell
  - `initializePlatform()` - setup orphan manager and signal handlers
- **Constants**: Platform-specific defaults and configuration
- **Initialization**: Automatic setup of orphan manager and cleanup handlers when module is imported
- **Documentation**: Comprehensive JSDoc with usage examples:
  - Basic spawning examples
  - Retry configuration examples
  - Platform-specific notes and limitations
  - Error handling patterns

### interactive-agent\src\utils\input.ts(MODIFY)

References: 

- interactive-agent\src\utils\temp-file-manager.ts(NEW)
- interactive-agent\src\utils\retry-manager.ts(NEW)
- interactive-agent\src\platform\index.ts(MODIFY)
- interactive-agent\src\constants.ts(MODIFY)

Enhance the existing input utility to leverage the new temporary file management, retry system, and improved error handling while maintaining backward compatibility. Modifications:

- **Import New Dependencies**: Add imports for:
  - `TempFileManager` from temp-file-manager
  - `spawnWithRetry` from platform/process-spawn
  - New timeout constants from constants
- **Enhanced getUserInput Function**: Add optional parameters:
  - `useTerminalIfAvailable?: boolean = false` - enable terminal spawning
  - `retryPolicy?: RetryPolicy` - custom retry configuration
  - `abortSignal?: AbortSignal` - for cancellation support
- **File-Based Communication Mode**: When terminal spawning is enabled:
  - Use `TempFileManager` to create response, heartbeat, and options files
  - Implement heartbeat monitoring similar to original MCP but with improved error handling
  - Use retry logic for file operations and spawn attempts
  - Automatic cleanup of temporary files on completion or error
- **Improved Error Handling**: Enhanced error management:
  - Wrap all operations in try-catch with proper cleanup
  - Use `AbortController` for timeout management
  - Aggregate errors from multiple retry attempts
  - Graceful fallback from terminal to console mode on any failure
- **Timeout Management**: Use new timeout constants:
  - Separate timeouts for spawn, heartbeat, and user input
  - Adaptive timeout adjustment based on platform capabilities
- **Backward Compatibility**: Ensure existing console-only usage continues to work without changes
- **Resource Management**: Proper cleanup of all resources (files, watchers, timeouts, processes) using the new management systems